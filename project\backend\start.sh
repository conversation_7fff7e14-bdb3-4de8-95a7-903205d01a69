#!/bin/bash

echo "🚀 Démarrage du serveur DEMUCS..."

# Vérifier si l'environnement virtuel existe
if [ ! -d "venv" ]; then
    echo "❌ Environnement virtuel non trouvé. Exécutez d'abord install.sh"
    exit 1
fi

# Activer l'environnement virtuel
echo "🔄 Activation de l'environnement virtuel..."
source venv/bin/activate

# Vérifier si DEMUCS est installé
python -c "import demucs" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ DEMUCS non installé. Exécutez d'abord install.sh"
    exit 1
fi

echo "✅ Démarrage du serveur sur http://localhost:5000"
echo "📱 Interface web disponible sur http://localhost:5174"
echo "🛑 Appuyez sur Ctrl+C pour arrêter"
echo ""

# Démarrer le serveur
python app.py
