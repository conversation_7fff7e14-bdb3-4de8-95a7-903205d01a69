import { Stem, ProcessingStep } from '../types/audio'

const BACKEND_URL = 'http://localhost:5000'

export interface DemucsTask {
  task_id: string
  status: 'uploaded' | 'processing' | 'completed' | 'error'
  progress: number
  current_step?: string
  stems?: DemucsStem[]
  error?: string
}

export interface DemucsStem {
  name: string
  type: string
  filename: string
  path: string
  peak_level: number
  rms_level: number
  quality: number
}

export class DemucsService {
  private static pollingIntervals = new Map<string, NodeJS.Timeout>()

  // Vérifier si le backend est disponible
  static async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${BACKEND_URL}/health`)
      const data = await response.json()
      return data.status === 'ok' && data.model_loaded
    } catch (error) {
      console.error('Backend DEMUCS non disponible:', error)
      return false
    }
  }

  // Upload et démarrer la séparation
  static async separateAudio(
    file: File,
    onProgress: (step: ProcessingStep) => void
  ): Promise<Stem[]> {
    try {
      // 1. Vérifier la disponibilité du backend
      const isHealthy = await this.checkHealth()
      if (!isHealthy) {
        throw new Error('Backend DEMUCS non disponible. Assurez-vous que le serveur Python est démarré.')
      }

      // 2. Upload du fichier
      onProgress({
        id: 'upload',
        name: 'Upload du fichier',
        description: 'Envoi du fichier vers le serveur DEMUCS',
        progress: 0,
        status: 'processing'
      })

      const formData = new FormData()
      formData.append('audio', file)

      const uploadResponse = await fetch(`${BACKEND_URL}/upload`, {
        method: 'POST',
        body: formData
      })

      if (!uploadResponse.ok) {
        const error = await uploadResponse.json()
        throw new Error(error.error || 'Erreur lors de l\'upload')
      }

      const uploadData = await uploadResponse.json()
      const taskId = uploadData.task_id

      onProgress({
        id: 'upload',
        name: 'Upload du fichier',
        description: 'Fichier uploadé avec succès',
        progress: 100,
        status: 'completed'
      })

      // 3. Démarrer la séparation
      const separateResponse = await fetch(`${BACKEND_URL}/separate/${taskId}`, {
        method: 'POST'
      })

      if (!separateResponse.ok) {
        const error = await separateResponse.json()
        throw new Error(error.error || 'Erreur lors du démarrage de la séparation')
      }

      // 4. Polling pour suivre le progrès
      return new Promise((resolve, reject) => {
        const pollStatus = async () => {
          try {
            const statusResponse = await fetch(`${BACKEND_URL}/status/${taskId}`)
            const taskData: DemucsTask = await statusResponse.json()

            // Mettre à jour le progrès
            onProgress({
              id: 'separation',
              name: 'Séparation DEMUCS',
              description: taskData.current_step || 'Traitement en cours...',
              progress: taskData.progress,
              status: taskData.status === 'completed' ? 'completed' : 'processing'
            })

            if (taskData.status === 'completed') {
              // Conversion des stems DEMUCS vers le format de l'app
              const stems = await this.convertDemucsStems(taskId, taskData.stems || [])
              
              // Nettoyer les ressources
              this.stopPolling(taskId)
              
              resolve(stems)
            } else if (taskData.status === 'error') {
              this.stopPolling(taskId)
              reject(new Error(taskData.error || 'Erreur lors de la séparation'))
            }
            // Sinon, continuer le polling
          } catch (error) {
            this.stopPolling(taskId)
            reject(error)
          }
        }

        // Démarrer le polling toutes les 2 secondes
        const interval = setInterval(pollStatus, 2000)
        this.pollingIntervals.set(taskId, interval)
        
        // Premier appel immédiat
        pollStatus()

        // Timeout de sécurité (10 minutes)
        setTimeout(() => {
          this.stopPolling(taskId)
          reject(new Error('Timeout: La séparation a pris trop de temps'))
        }, 10 * 60 * 1000)
      })

    } catch (error) {
      console.error('Erreur lors de la séparation DEMUCS:', error)
      throw error
    }
  }

  // Convertir les stems DEMUCS vers le format de l'application
  private static async convertDemucsStems(taskId: string, demucsStems: DemucsStem[]): Promise<Stem[]> {
    const colorMap: Record<string, string> = {
      'vocals': '#F59E0B',
      'drums': '#EF4444', 
      'bass': '#8B5CF6',
      'other': '#10B981'
    }

    const stems: Stem[] = []

    for (const demucsStem of demucsStems) {
      try {
        // Télécharger le fichier audio du stem
        const audioResponse = await fetch(`${BACKEND_URL}/download/${taskId}/${demucsStem.type}`)
        if (audioResponse.ok) {
          const audioBlob = await audioResponse.blob()
          const audioUrl = URL.createObjectURL(audioBlob)

          const stem: Stem = {
            id: demucsStem.type,
            name: demucsStem.name,
            type: demucsStem.type as any,
            color: colorMap[demucsStem.type] || '#6B7280',
            quality: demucsStem.quality,
            confidence: Math.min(0.95, demucsStem.quality + 0.1),
            audioUrl: audioUrl,
            waveformData: Array.from({ length: 100 }, () => Math.random() * 0.8 + 0.1),
            metadata: {
              peakLevel: demucsStem.peak_level,
              rmsLevel: demucsStem.rms_level,
              dynamicRange: Math.abs(demucsStem.peak_level - demucsStem.rms_level),
              spectralCentroid: 2000 // Valeur par défaut
            }
          }

          stems.push(stem)
        }
      } catch (error) {
        console.error(`Erreur lors du téléchargement du stem ${demucsStem.name}:`, error)
      }
    }

    return stems
  }

  // Arrêter le polling pour une tâche
  private static stopPolling(taskId: string) {
    const interval = this.pollingIntervals.get(taskId)
    if (interval) {
      clearInterval(interval)
      this.pollingIntervals.delete(taskId)
    }
  }

  // Nettoyer une tâche sur le serveur
  static async cleanupTask(taskId: string): Promise<void> {
    try {
      await fetch(`${BACKEND_URL}/cleanup/${taskId}`, {
        method: 'DELETE'
      })
    } catch (error) {
      console.error('Erreur lors du nettoyage:', error)
    }
  }

  // Arrêter tous les pollings
  static cleanup() {
    this.pollingIntervals.forEach((interval) => {
      clearInterval(interval)
    })
    this.pollingIntervals.clear()
  }
}
