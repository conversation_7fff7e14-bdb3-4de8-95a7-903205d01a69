# 🎵 Guide d'Installation DEMUCS - Séparation Audio Réelle

Ce guide vous explique comment installer et utiliser la séparation audio réelle avec DEMUCS.

## 🔧 Prérequis

### Système Requis
- **Python 3.8+** installé
- **Node.js 16+** pour le frontend
- **4GB+ RAM** recommandé
- **GPU NVIDIA** (optionnel, mais recommandé pour de meilleures performances)

### Vérification Python
```bash
python --version  # ou python3 --version
pip --version     # ou pip3 --version
```

## 📦 Installation

### 1. **Installation du Backend DEMUCS**

#### Sur Windows :
```cmd
cd project/backend
install.bat
```

#### Sur Linux/Mac :
```bash
cd project/backend
chmod +x install.sh
./install.sh
```

### 2. **Installation Manuelle (si les scripts échouent)**

```bash
cd project/backend

# Créer l'environnement virtuel
python -m venv venv

# Activer l'environnement (Windows)
venv\Scripts\activate.bat

# Activer l'environnement (Linux/Mac)
source venv/bin/activate

# Installer les dépendances
pip install --upgrade pip
pip install -r requirements.txt
```

## 🚀 Démarrage

### 1. **Démarrer le Backend DEMUCS**

#### Méthode Rapide :
```bash
# Windows
cd project/backend
start.bat

# Linux/Mac
cd project/backend
chmod +x start.sh
./start.sh
```

#### Méthode Manuelle :
```bash
cd project/backend
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate.bat  # Windows

python app.py
```

### 2. **Démarrer le Frontend**

Dans un autre terminal :
```bash
cd project
npm run dev
```

## 🧪 Test de la Séparation Réelle

### 1. **Vérification du Backend**

Ouvrir http://localhost:5000/health dans le navigateur.
Vous devriez voir :
```json
{
  "status": "ok",
  "model_loaded": true,
  "device": "cuda",  // ou "cpu"
  "cuda_available": true
}
```

### 2. **Test dans l'Application**

1. **Ouvrir** l'application : http://localhost:5174
2. **Vérifier** que le toggle "🎵 DEMUCS Réel" est disponible
3. **Télécharger** un fichier audio (MP3, WAV, etc.)
4. **Activer** le mode "DEMUCS Réel"
5. **Cliquer** sur "🎵 Séparation DEMUCS Réelle"
6. **Attendre** la séparation (peut prendre 1-5 minutes selon le fichier)

### 3. **Logs à Observer**

#### Console Frontend (F12) :
```
🔍 Backend DEMUCS: Disponible
🚀 Démarrage du traitement...
🎵 Démarrage de la séparation réelle avec DEMUCS...
📊 Upload du fichier: 100%
📊 Séparation DEMUCS: 30%
✅ Séparation réelle terminée: 4 stems
```

#### Console Backend :
```
🔄 Chargement du modèle DEMUCS...
✅ Modèle DEMUCS chargé sur cuda
🚀 Démarrage du serveur sur http://localhost:5000
📊 task_id: 10% - Chargement du fichier audio
📊 task_id: 30% - Séparation en cours avec DEMUCS
✅ Séparation terminée pour task_id
```

## 🎯 Différences Réel vs Simulation

| Aspect | Simulation | DEMUCS Réel |
|--------|------------|-------------|
| **Temps** | ~15 secondes | 1-5 minutes |
| **Qualité** | Audio généré | Vraie séparation |
| **CPU/GPU** | Minimal | Intensif |
| **Stems** | 4 simulés | 4 réels (drums, bass, other, vocals) |
| **Fichiers** | Générés | Vrais fichiers WAV |

## 🔍 Dépannage

### **Backend ne démarre pas**

#### Erreur Python :
```bash
# Vérifier la version Python
python --version

# Réinstaller les dépendances
pip install --upgrade pip
pip install torch torchaudio demucs flask flask-cors
```

#### Erreur CUDA :
```bash
# Installer PyTorch avec CUDA
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
```

#### Erreur de mémoire :
- Réduire la taille du fichier audio (<50MB)
- Fermer d'autres applications
- Utiliser un fichier plus court (<3 minutes)

### **Frontend ne se connecte pas**

#### Vérifications :
1. Backend démarré sur http://localhost:5000
2. Pas d'erreur CORS dans la console
3. Firewall/antivirus ne bloque pas

#### Test manuel :
```bash
curl http://localhost:5000/health
```

### **Séparation échoue**

#### Causes communes :
- Fichier audio corrompu
- Format non supporté
- Manque de mémoire
- Modèle DEMUCS non chargé

#### Solutions :
1. Essayer un fichier MP3 simple
2. Réduire la taille/durée
3. Redémarrer le backend
4. Vérifier les logs d'erreur

## 📊 Performance

### **Temps de Traitement Typiques**

| Durée Audio | CPU | GPU (CUDA) |
|-------------|-----|------------|
| 30 secondes | ~1 min | ~20 sec |
| 2 minutes | ~4 min | ~1 min |
| 5 minutes | ~10 min | ~3 min |

### **Optimisations**

1. **GPU NVIDIA** : 3-5x plus rapide
2. **Fichiers courts** : Tester avec <1 minute d'abord
3. **Format WAV** : Légèrement plus rapide que MP3
4. **RAM** : 8GB+ recommandé pour gros fichiers

## ✅ Checklist de Test

- [ ] Python 3.8+ installé
- [ ] Backend DEMUCS installé sans erreur
- [ ] Serveur backend démarre sur :5000
- [ ] Frontend démarre sur :5174
- [ ] Health check retourne "ok"
- [ ] Toggle "DEMUCS Réel" disponible
- [ ] Upload de fichier fonctionne
- [ ] Séparation démarre et progresse
- [ ] 4 stems réels générés
- [ ] Audio de qualité dans les stems
- [ ] Téléchargement des stems fonctionne

## 🎉 Résultat Attendu

Après installation réussie, vous devriez pouvoir :

1. **Télécharger** un vrai fichier audio
2. **Activer** le mode "DEMUCS Réel"
3. **Lancer** la séparation
4. **Obtenir** 4 stems de vraie qualité :
   - 🥁 **Drums** : Vraie batterie isolée
   - 🎸 **Bass** : Vraie ligne de basse
   - 🎵 **Other** : Instruments (guitare, piano, etc.)
   - 🎤 **Vocals** : Vraies voix isolées
5. **Écouter** chaque stem individuellement
6. **Télécharger** les fichiers WAV réels

**La séparation DEMUCS réelle fonctionne ! 🎵**
