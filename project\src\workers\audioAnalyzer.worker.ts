// Web Worker pour l'analyse audio en arrière-plan
import { AudioAnalysis } from '../types/audio'

self.onmessage = async function(e) {
  const { audioBuffer, sampleRate } = e.data

  try {
    // Analyse audio optimisée
    const analysis = await analyzeAudioBuffer(audioBuffer, sampleRate)
    self.postMessage({ success: true, analysis })
  } catch (error) {
    self.postMessage({ success: false, error: error.message })
  }
}

async function analyzeAudioBuffer(audioBuffer: ArrayBuffer, sampleRate: number): Promise<AudioAnalysis> {
  // Créer un contexte audio offline pour l'analyse
  const audioContext = new OfflineAudioContext(1, audioBuffer.byteLength / 4, sampleRate)
  
  // Décoder le buffer audio
  const decodedBuffer = await audioContext.decodeAudioData(audioBuffer.slice(0))
  
  const duration = decodedBuffer.duration
  const channels = decodedBuffer.numberOfChannels
  
  // Analyser le premier canal
  const channelData = decodedBuffer.getChannelData(0)
  
  // Calcul du niveau RMS (optimisé)
  let sumSquares = 0
  let peak = 0
  const step = Math.max(1, Math.floor(channelData.length / 10000)) // Échantillonnage pour la performance
  
  for (let i = 0; i < channelData.length; i += step) {
    const sample = Math.abs(channelData[i])
    sumSquares += sample * sample
    if (sample > peak) peak = sample
  }
  
  const rms = Math.sqrt(sumSquares / (channelData.length / step))
  const loudness = 20 * Math.log10(rms + 1e-10) // Éviter log(0)
  const dynamicRange = 20 * Math.log10(peak / (rms + 1e-10))
  
  // Analyse spectrale simplifiée (centroïde spectral approximatif)
  const fftSize = 2048
  const fft = new Float32Array(fftSize)
  const startIndex = Math.floor(channelData.length / 2) - fftSize / 2
  
  for (let i = 0; i < fftSize && startIndex + i < channelData.length; i++) {
    fft[i] = channelData[startIndex + i]
  }
  
  // Calcul approximatif du centroïde spectral
  let weightedSum = 0
  let magnitudeSum = 0
  
  for (let i = 1; i < fftSize / 2; i++) {
    const magnitude = Math.abs(fft[i])
    const frequency = (i * sampleRate) / fftSize
    weightedSum += frequency * magnitude
    magnitudeSum += magnitude
  }
  
  const spectralCentroid = magnitudeSum > 0 ? weightedSum / magnitudeSum : 0
  
  return {
    sampleRate,
    duration,
    channels,
    bitRate: Math.round((audioBuffer.byteLength * 8) / duration),
    format: 'AUDIO', // Format générique
    loudness,
    dynamicRange: Math.max(0, dynamicRange),
    spectralCentroid
  }
}

export {}
