// Web Worker ultra-rapide pour l'analyse audio
interface AudioAnalysis {
  sampleRate: number;
  duration: number;
  channels: number;
  bitRate: number;
  format: string;
  loudness: number;
  dynamicRange: number;
  spectralCentroid: number;
}

self.onmessage = async function(e) {
  const { audioBuffer, fileSize, fileType } = e.data

  try {
    // Analyse ultra-rapide
    const analysis = await fastAnalyzeAudio(audioBuffer, fileSize, fileType)
    self.postMessage({ success: true, analysis })
  } catch (error) {
    // Fallback avec estimation basée sur la taille
    const fallbackAnalysis = estimateFromFileSize(fileSize, fileType)
    self.postMessage({ success: true, analysis: fallbackAnalysis })
  }
}

async function fastAnalyzeAudio(audioBuffer: ArrayBuffer, fileSize: number, fileType: string): Promise<AudioAnalysis> {
  // Pour les gros fichiers (>5MB), utiliser seulement l'estimation
  if (audioBuffer.byteLength > 5 * 1024 * 1024) {
    return estimateFromFileSize(fileSize, fileType)
  }

  try {
    // Contexte audio offline pour de meilleures performances
    const audioContext = new OfflineAudioContext(1, 44100, 44100)
    const decodedBuffer = await audioContext.decodeAudioData(audioBuffer.slice(0))

    const duration = decodedBuffer.duration
    const channels = decodedBuffer.numberOfChannels
    const sampleRate = decodedBuffer.sampleRate

    // Échantillonnage ultra-agressif (1 échantillon sur 5000)
    const channelData = decodedBuffer.getChannelData(0)
    const step = Math.max(5000, Math.floor(channelData.length / 1000))

    let sumSquares = 0
    let peak = 0
    let sampleCount = 0

    // Analyse rapide avec échantillonnage
    for (let i = 0; i < channelData.length; i += step) {
      const sample = channelData[i]
      const absSample = Math.abs(sample)
      sumSquares += sample * sample
      if (absSample > peak) peak = absSample
      sampleCount++
    }

    const rms = Math.sqrt(sumSquares / sampleCount)
    const loudness = rms > 0 ? Math.max(-60, 20 * Math.log10(rms)) : -60
    const dynamicRange = (peak > 0 && rms > 0) ? Math.min(60, 20 * Math.log10(peak / rms)) : 25

    // Centroïde spectral approximatif basé sur le type de fichier
    let spectralCentroid = 2000
    if (fileType.includes('mp3')) spectralCentroid = 1800
    else if (fileType.includes('wav')) spectralCentroid = 2200
    else if (fileType.includes('flac')) spectralCentroid = 2400

    return {
      sampleRate,
      duration,
      channels,
      bitRate: Math.round((audioBuffer.byteLength * 8) / duration),
      format: fileType.split('/')[1]?.toUpperCase() || 'AUDIO',
      loudness,
      dynamicRange,
      spectralCentroid
    }
  } catch (error) {
    // Fallback en cas d'erreur
    return estimateFromFileSize(fileSize, fileType)
  }
}

function estimateFromFileSize(fileSize: number, fileType: string): AudioAnalysis {
  // Estimation basée sur la taille et le type de fichier
  let estimatedBitRate = 128000 // 128 kbps par défaut

  // Estimation du bitrate selon le type
  if (fileType.includes('mp3')) {
    if (fileSize < 3 * 1024 * 1024) estimatedBitRate = 128000 // 128k
    else if (fileSize < 6 * 1024 * 1024) estimatedBitRate = 192000 // 192k
    else estimatedBitRate = 320000 // 320k
  } else if (fileType.includes('wav') || fileType.includes('flac')) {
    estimatedBitRate = 1411000 // 1411k (CD quality)
  }

  const estimatedDuration = (fileSize * 8) / estimatedBitRate

  // Valeurs typiques basées sur le type de fichier
  const loudness = fileType.includes('wav') ? -16 : -20
  const dynamicRange = fileType.includes('flac') ? 30 : 25
  const spectralCentroid = fileType.includes('mp3') ? 1800 : 2200

  return {
    sampleRate: 44100,
    duration: estimatedDuration,
    channels: 2,
    bitRate: estimatedBitRate,
    format: fileType.split('/')[1]?.toUpperCase() || 'AUDIO',
    loudness,
    dynamicRange,
    spectralCentroid
  }
}

export {}
