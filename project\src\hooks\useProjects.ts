import { useState, useEffect, useCallback, useMemo } from 'react'
import { DatabaseService } from '../services/database'
import { DatabaseProject } from '../lib/supabase'
import { AudioAnalysis, SeparationSettings, Stem } from '../types/audio'

export interface ProjectWithData extends DatabaseProject {
  analysis?: AudioAnalysis
  settings?: SeparationSettings
  stems?: Stem[]
}

export const useProjects = () => {
  const [projects, setProjects] = useState<DatabaseProject[]>([])
  const [currentProject, setCurrentProject] = useState<ProjectWithData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastFetch, setLastFetch] = useState<number>(0)

  // Cache pour éviter les requêtes répétées
  const CACHE_DURATION = 30000 // 30 secondes

  // Charger tous les projets avec cache
  const loadProjects = useCallback(async (forceRefresh = false) => {
    const now = Date.now()

    // Utiliser le cache si les données sont récentes
    if (!forceRefresh && projects.length > 0 && (now - lastFetch) < CACHE_DURATION) {
      return
    }

    try {
      setLoading(true)
      setError(null)
      const projectsData = await DatabaseService.getAllProjects()
      setProjects(projectsData)
      setLastFetch(now)
    } catch (err) {
      setError('Erreur lors du chargement des projets')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }, [projects.length, lastFetch])

  // Créer un nouveau projet
  const createProject = async (
    name: string, 
    file: File
  ): Promise<string | null> => {
    try {
      setLoading(true)
      setError(null)
      
      const projectId = await DatabaseService.createProject(
        name,
        file.name,
        file.size,
        0 // La durée sera mise à jour après l'analyse
      )
      
      // Recharger la liste des projets
      await loadProjects()
      
      return projectId
    } catch (err) {
      setError('Erreur lors de la création du projet')
      console.error(err)
      return null
    } finally {
      setLoading(false)
    }
  }

  // Charger un projet avec toutes ses données
  const loadProjectWithData = async (projectId: string): Promise<ProjectWithData | null> => {
    try {
      setLoading(true)
      setError(null)

      const [project, analysis, settings, stems] = await Promise.all([
        DatabaseService.getProject(projectId),
        DatabaseService.getAudioAnalysis(projectId),
        DatabaseService.getSeparationSettings(projectId),
        DatabaseService.getStems(projectId)
      ])

      if (!project) {
        setError('Projet non trouvé')
        return null
      }

      const projectWithData: ProjectWithData = {
        ...project,
        analysis: analysis || undefined,
        settings: settings || undefined,
        stems: stems.length > 0 ? stems : undefined
      }

      setCurrentProject(projectWithData)
      return projectWithData
    } catch (err) {
      setError('Erreur lors du chargement du projet')
      console.error(err)
      return null
    } finally {
      setLoading(false)
    }
  }

  // Sauvegarder l'analyse audio
  const saveAnalysis = async (projectId: string, analysis: AudioAnalysis) => {
    try {
      await DatabaseService.saveAudioAnalysis(projectId, analysis)
      
      // Mettre à jour le projet courant si c'est le même
      if (currentProject && currentProject.id === projectId) {
        setCurrentProject({
          ...currentProject,
          analysis
        })
      }
    } catch (err) {
      setError('Erreur lors de la sauvegarde de l\'analyse')
      console.error(err)
    }
  }

  // Sauvegarder les paramètres de séparation
  const saveSettings = async (projectId: string, settings: SeparationSettings) => {
    try {
      await DatabaseService.saveSeparationSettings(projectId, settings)
      
      // Mettre à jour le projet courant si c'est le même
      if (currentProject && currentProject.id === projectId) {
        setCurrentProject({
          ...currentProject,
          settings
        })
      }
    } catch (err) {
      setError('Erreur lors de la sauvegarde des paramètres')
      console.error(err)
    }
  }

  // Sauvegarder les stems
  const saveStems = async (projectId: string, stems: Stem[]) => {
    try {
      await DatabaseService.saveStems(projectId, stems)
      
      // Mettre à jour le statut du projet
      await DatabaseService.updateProjectStatus(projectId, 'completed')
      
      // Mettre à jour le projet courant si c'est le même
      if (currentProject && currentProject.id === projectId) {
        setCurrentProject({
          ...currentProject,
          stems,
          status: 'completed'
        })
      }
      
      // Recharger la liste des projets
      await loadProjects()
    } catch (err) {
      setError('Erreur lors de la sauvegarde des stems')
      console.error(err)
    }
  }

  // Mettre à jour le statut d'un projet
  const updateProjectStatus = async (projectId: string, status: DatabaseProject['status']) => {
    try {
      await DatabaseService.updateProjectStatus(projectId, status)
      
      // Mettre à jour le projet courant si c'est le même
      if (currentProject && currentProject.id === projectId) {
        setCurrentProject({
          ...currentProject,
          status
        })
      }
      
      // Recharger la liste des projets
      await loadProjects()
    } catch (err) {
      setError('Erreur lors de la mise à jour du statut')
      console.error(err)
    }
  }

  // Charger les projets au montage du composant
  useEffect(() => {
    loadProjects()
  }, [])

  return {
    projects,
    currentProject,
    loading,
    error,
    loadProjects,
    createProject,
    loadProjectWithData,
    saveAnalysis,
    saveSettings,
    saveStems,
    updateProjectStatus,
    setCurrentProject,
    setError
  }
}
