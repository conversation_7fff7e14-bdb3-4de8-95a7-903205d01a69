import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Header } from './components/Header';
import { AudioUploader } from './components/AudioUploader';
import { AudioWaveform } from './components/AudioWaveform';
import { AudioAnalyzer } from './components/AudioAnalyzer';
import { AdvancedSettings } from './components/AdvancedSettings';
import { ProcessingDialog } from './components/ProcessingDialog';
import { StemPlayer } from './components/StemPlayer';
import { ProjectHistory } from './components/ProjectHistory';
import { DebugPanel } from './components/DebugPanel';
import { Play, Pause, RotateCcw } from 'lucide-react';
import { Stem, SeparationSettings, ProcessingStep, AudioAnalysis } from './types/audio';
import { useProjects } from './hooks/useProjects';
import { DatabaseProject } from './lib/supabase';
import { AudioSeparationService } from './services/demucsService';

function App() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [audioAnalysis, setAudioAnalysis] = useState<AudioAnalysis | null>(null);
  const [settings, setSettings] = useState<SeparationSettings>({
    model: 'htdemucs',
    quality: 'high',
    stemCount: 4,
    vocals: {
      enabled: true,
      denoising: true,
      enhancement: true,
      pitch_correction: false
    },
    instruments: {
      enabled: true,
      separation_level: 'advanced'
    },
    postProcessing: {
      normalize: true,
      fade_in: 0,
      fade_out: 0,
      noise_reduction: 30
    }
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingSteps, setProcessingSteps] = useState<ProcessingStep[]>([]);
  const [currentStepId, setCurrentStepId] = useState('');
  const [processingStartTime, setProcessingStartTime] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [stems, setStems] = useState<Stem[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);
  const [backendAvailable, setBackendAvailable] = useState(false);
  const [availableModels, setAvailableModels] = useState<string[]>([]);

  const audioRef = useRef<HTMLAudioElement>(null);
  const processingIntervalRef = useRef<NodeJS.Timeout>();

  // Hook pour gérer les projets et la base de données
  const {
    projects,
    currentProject,
    loading: projectsLoading,
    error: projectsError,
    createProject,
    loadProjectWithData,
    saveAnalysis,
    saveSettings,
    saveStems,
    updateProjectStatus,
    setError: setProjectsError
  } = useProjects();

  // Vérifier la disponibilité du backend au chargement
  useEffect(() => {
    const checkBackend = async () => {
      const health = await AudioSeparationService.checkHealth();
      setBackendAvailable(health.available);
      setAvailableModels(health.models);
      console.log('🔍 Backend de séparation:', health.available ? 'Disponible' : 'Non disponible');
      console.log('🎵 Modèles disponibles:', health.models);
    };

    checkBackend();
  }, []);

  const handleFileSelect = async (file: File) => {
    setSelectedFile(file);
    setStems([]);
    setAudioAnalysis(null);
    setCurrentProjectId(null);

    // Create audio URL for playback
    if (audioRef.current) {
      audioRef.current.src = URL.createObjectURL(file);
    }

    // Créer un nouveau projet dans la base de données
    const projectName = file.name.replace(/\.[^/.]+$/, ""); // Enlever l'extension
    const projectId = await createProject(projectName, file);
    if (projectId) {
      setCurrentProjectId(projectId);
    }
  };

  const handleAnalysisComplete = useCallback(async (analysis: AudioAnalysis) => {
    setAudioAnalysis(analysis);

    // Sauvegarder l'analyse dans la base de données avec debouncing
    if (currentProjectId) {
      // Utiliser un timeout pour éviter les sauvegardes multiples
      setTimeout(async () => {
        try {
          await saveAnalysis(currentProjectId, analysis);
        } catch (error) {
          console.error('Erreur lors de la sauvegarde de l\'analyse:', error);
        }
      }, 500);
    }
  }, [currentProjectId, saveAnalysis]);

  const handleProjectSelect = async (project: DatabaseProject) => {
    const projectWithData = await loadProjectWithData(project.id);
    if (projectWithData) {
      setCurrentProjectId(project.id);

      // Charger les données du projet
      if (projectWithData.analysis) {
        setAudioAnalysis(projectWithData.analysis);
      }
      if (projectWithData.settings) {
        setSettings(projectWithData.settings);
      }
      if (projectWithData.stems) {
        setStems(projectWithData.stems);
      }

      // Note: Le fichier original n'est pas rechargé car il n'est pas stocké
      // Dans une vraie application, vous stockeriez le fichier dans Supabase Storage
      setSelectedFile(null);
    }
  };

  const getEstimatedProcessingTime = () => {
    if (!audioAnalysis) return 180; // 3 minutes par défaut
    
    const baseTime = audioAnalysis.duration * 2; // 2x la durée du fichier
    const qualityMultiplier = {
      draft: 0.5,
      standard: 1,
      high: 2,
      ultra: 4
    }[settings.quality];
    
    const modelMultiplier = {
      'spleeter-5stems': 0.5,
      'demucs-v4': 1.5,
      'htdemucs': 3,
      'mdx-extra': 2
    }[settings.model];
    
    return Math.round(baseTime * qualityMultiplier * modelMultiplier);
  };

  const startProcessing = async () => {
    console.log('🚀 Démarrage du traitement...');

    if (!backendAvailable) {
      alert('❌ Backend de séparation non disponible. Veuillez démarrer le serveur Python.');
      return;
    }

    if (!selectedFile) {
      alert('❌ Veuillez sélectionner un fichier audio.');
      return;
    }

    setIsProcessing(true);
    setProcessingStartTime(Date.now());
    setElapsedTime(0);
    setStems([]); // Reset des stems précédents

    // Mettre à jour le statut du projet
    if (currentProjectId) {
      await updateProjectStatus(currentProjectId, 'processing');
      await saveSettings(currentProjectId, settings);
    }

    await startRealSeparation();
  };

  const startRealSeparation = async () => {
    const modelToUse = settings.model === 'htdemucs' ? 'demucs' : 'spleeter';
    console.log(`🎵 Démarrage de la séparation avec ${modelToUse.toUpperCase()}...`);

    try {
      // Timer pour le temps écoulé
      processingIntervalRef.current = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - processingStartTime) / 1000));
      }, 1000);

      const stems = await AudioSeparationService.separateAudio(selectedFile!, modelToUse, (step) => {
        console.log(`📊 ${step.name}: ${step.progress}%`);

        // Mettre à jour les étapes de traitement
        setProcessingSteps(prev => {
          const existingIndex = prev.findIndex(s => s.id === step.id);
          if (existingIndex >= 0) {
            const newSteps = [...prev];
            newSteps[existingIndex] = step;
            return newSteps;
          } else {
            return [...prev, step];
          }
        });

        setCurrentStepId(step.id);
      });

      console.log(`✅ Séparation ${modelToUse} terminée:`, stems.length, 'stems');
      setStems(stems);

      // Sauvegarder les stems dans la base de données
      if (currentProjectId) {
        await saveStems(currentProjectId, stems);
      }

    } catch (error) {
      console.error('❌ Erreur lors de la séparation:', error);
      alert(`❌ Erreur lors de la séparation: ${error.message}`);
    } finally {
      setIsProcessing(false);
      if (processingIntervalRef.current) {
        clearInterval(processingIntervalRef.current);
      }
    }
  };



  const generateStems = async () => {
    console.log('🎵 Début de la génération des stems...');

    const generatedStems: Stem[] = [
      {
        id: 'vocal',
        name: 'Voix',
        type: 'vocal',
        color: '#F59E0B',
        quality: 0.92,
        confidence: 0.89,
        waveformData: Array.from({ length: 100 }, () => Math.random() * 0.8 + 0.1),
        metadata: {
          peakLevel: -3.2,
          rmsLevel: -18.5,
          dynamicRange: 12.8,
          spectralCentroid: 2400
        }
      },
      {
        id: 'instrumental',
        name: 'Instrumental',
        type: 'instrumental',
        color: '#10B981',
        quality: 0.88,
        confidence: 0.94,
        waveformData: Array.from({ length: 100 }, () => Math.random() * 0.9 + 0.05),
        metadata: {
          peakLevel: -1.8,
          rmsLevel: -16.2,
          dynamicRange: 15.4,
          spectralCentroid: 1800
        }
      },
      {
        id: 'drums',
        name: 'Batterie',
        type: 'drums',
        color: '#EF4444',
        quality: 0.85,
        confidence: 0.91,
        waveformData: Array.from({ length: 100 }, () => Math.random() * 0.7 + 0.2),
        metadata: {
          peakLevel: -0.5,
          rmsLevel: -14.8,
          dynamicRange: 18.2,
          spectralCentroid: 3200
        }
      },
      {
        id: 'bass',
        name: 'Basse',
        type: 'bass',
        color: '#8B5CF6',
        quality: 0.79,
        confidence: 0.86,
        waveformData: Array.from({ length: 100 }, () => Math.random() * 0.6 + 0.1),
        metadata: {
          peakLevel: -4.1,
          rmsLevel: -20.3,
          dynamicRange: 10.5,
          spectralCentroid: 800
        }
      }
    ];

    console.log('✅ Stems générés:', generatedStems.length, 'stems');
    setStems(generatedStems);

    // Sauvegarder les stems dans la base de données
    if (currentProjectId) {
      console.log('💾 Sauvegarde des stems en base...');
      await saveStems(currentProjectId, generatedStems);
      console.log('✅ Stems sauvegardés en base');
    }

    console.log('🎉 Génération des stems terminée !');
  };

  const togglePlayback = () => {
    if (!audioRef.current) return;
    
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const resetAudio = () => {
    if (!audioRef.current) return;
    
    audioRef.current.currentTime = 0;
    setCurrentTime(0);
    if (isPlaying) {
      setIsPlaying(false);
      audioRef.current.pause();
    }
  };

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleEnded = () => setIsPlaying(false);

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [selectedFile]);

  useEffect(() => {
    return () => {
      if (processingIntervalRef.current) {
        clearInterval(processingIntervalRef.current);
      }
    };
  }, []);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Upload Section */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6">
                Séparation Audio Professionnelle
              </h2>
              
              <AudioUploader 
                onFileSelect={handleFileSelect}
                isProcessing={isProcessing}
              />
            </div>

            {/* Audio Analysis */}
            {selectedFile && (
              <AudioAnalyzer
                audioFile={selectedFile}
                onAnalysisComplete={handleAnalysisComplete}
              />
            )}

            {/* Advanced Settings */}
            {selectedFile && (
              <AdvancedSettings
                settings={settings}
                onSettingsChange={setSettings}
                isProcessing={isProcessing}
              />
            )}

            {/* Audio Player */}
            {selectedFile && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                    {selectedFile.name}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </span>
                  </div>
                </div>

                <AudioWaveform
                  audioFile={selectedFile}
                  isPlaying={isPlaying}
                  currentTime={currentTime}
                  duration={duration}
                />

                <div className="flex items-center justify-center space-x-4 mt-4">
                  <button
                    onClick={resetAudio}
                    className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                  >
                    <RotateCcw className="w-5 h-5" />
                  </button>
                  
                  <button
                    onClick={togglePlayback}
                    className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 text-white rounded-full hover:from-purple-600 hover:to-blue-700 transition-all"
                  >
                    {isPlaying ? (
                      <Pause className="w-5 h-5" />
                    ) : (
                      <Play className="w-5 h-5 ml-0.5" />
                    )}
                  </button>

                  {/* Statut du backend */}
                  <div className="flex items-center space-x-3 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Statut:
                    </span>
                    <span className={`text-sm font-medium ${backendAvailable ? 'text-green-600' : 'text-red-600'}`}>
                      {backendAvailable ? '✅ Backend Prêt' : '❌ Backend Non Disponible'}
                    </span>
                    {availableModels.length > 0 && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        ({availableModels.map(m => m.toUpperCase()).join(', ')})
                      </span>
                    )}
                  </div>

                  <button
                    onClick={startProcessing}
                    disabled={isProcessing || !selectedFile || !backendAvailable}
                    className={`
                      px-6 py-2 rounded-lg font-medium transition-all
                      ${isProcessing || !selectedFile || !backendAvailable
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700'
                      }
                    `}
                  >
                    {isProcessing
                      ? 'Traitement en cours...'
                      : !backendAvailable
                      ? '❌ Backend Non Disponible'
                      : `🎵 Lancer la Séparation (${settings.model.toUpperCase()})`
                    }
                  </button>

                  {stems.length > 0 && (
                    <button
                      onClick={() => {
                        setStems([]);
                        setProcessingSteps([]);
                        setCurrentStepId('');
                        setElapsedTime(0);
                        console.log('🔄 Reset des stems effectué');
                      }}
                      className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all"
                    >
                      Nouveau traitement
                    </button>
                  )}
                </div>

                <audio ref={audioRef} />
              </div>
            )}
          </div>

          {/* Results Section */}
          <div className="space-y-6">
            {/* Debug Panel */}
            <DebugPanel
              isProcessing={isProcessing}
              stems={stems}
              processingSteps={processingSteps}
              onStartProcessing={startProcessing}
              onReset={() => {
                setStems([]);
                setProcessingSteps([]);
                setCurrentStepId('');
                setElapsedTime(0);
                setIsProcessing(false);
                if (processingIntervalRef.current) {
                  clearInterval(processingIntervalRef.current);
                }
                console.log('🔄 Reset complet effectué');
              }}
              selectedFile={selectedFile}
            />

            {/* Project History */}
            <ProjectHistory
              projects={projects}
              onSelectProject={handleProjectSelect}
              loading={projectsLoading}
            />

            {stems.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <StemPlayer stems={stems} originalFile={selectedFile} />
              </div>
            )}

            {/* Error Display */}
            {projectsError && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-xl p-4">
                <p className="text-red-600 dark:text-red-400 text-sm">
                  {projectsError}
                </p>
                <button
                  onClick={() => setProjectsError(null)}
                  className="mt-2 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm underline"
                >
                  Fermer
                </button>
              </div>
            )}

            {/* Info Panel */}
            <div className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-700">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                Comment ça marche ?
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li className="flex items-start space-x-2">
                  <span className="text-purple-500 font-bold">1.</span>
                  <span>Uploadez votre fichier audio</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-purple-500 font-bold">2.</span>
                  <span>Notre IA analyse et sépare les pistes</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-purple-500 font-bold">3.</span>
                  <span>Écoutez et téléchargez vos stems</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-purple-500 font-bold">4.</span>
                  <span>Retrouvez vos projets dans l'historique</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </main>

      <ProcessingDialog
        isOpen={isProcessing}
        steps={processingSteps}
        currentStepId={currentStepId}
        model={settings.model.toUpperCase()}
        estimatedTime={getEstimatedProcessingTime()}
        elapsedTime={elapsedTime}
      />
    </div>
  );
}

export default App;