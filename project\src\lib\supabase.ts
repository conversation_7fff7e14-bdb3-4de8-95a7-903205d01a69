import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://zdybqxlgroojyjqxfvep.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpkeWJxeGxncm9vanlqcXhmdmVwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2Nzg1MTIsImV4cCI6MjA2NTI1NDUxMn0.C0y8ALmIUtA3t6VV3Qqcfus_fwGGkj_iX6LTn0SH198'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types pour la base de données
export interface DatabaseProject {
  id: string
  name: string
  original_filename: string
  file_size: number
  duration: number
  created_at: string
  updated_at: string
  user_id?: string
  status: 'uploaded' | 'processing' | 'completed' | 'error'
}

export interface DatabaseAudioAnalysis {
  id: string
  project_id: string
  sample_rate: number
  duration: number
  channels: number
  bit_rate: number
  format: string
  loudness: number
  dynamic_range: number
  spectral_centroid: number
  created_at: string
}

export interface DatabaseSeparationSettings {
  id: string
  project_id: string
  model: string
  quality: string
  stem_count: number
  vocals_enabled: boolean
  vocals_denoising: boolean
  vocals_enhancement: boolean
  vocals_pitch_correction: boolean
  instruments_enabled: boolean
  instruments_separation_level: string
  post_processing_normalize: boolean
  post_processing_fade_in: number
  post_processing_fade_out: number
  post_processing_noise_reduction: number
  created_at: string
}

export interface DatabaseStem {
  id: string
  project_id: string
  name: string
  type: string
  color: string
  quality: number
  confidence: number
  file_url?: string
  peak_level: number
  rms_level: number
  dynamic_range: number
  spectral_centroid: number
  created_at: string
}

export interface DatabaseProcessingResult {
  id: string
  project_id: string
  processing_time: number
  model: string
  quality: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  error_message?: string
  created_at: string
  completed_at?: string
}
