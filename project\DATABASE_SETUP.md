# Configuration de la Base de Données Supabase

Ce guide vous explique comment configurer la base de données Supabase pour l'application de séparation audio.

## Étapes de Configuration

### 1. Accéder à Supabase

1. Connectez-vous à votre projet Supabase : https://zdybqxlgroojyjqxfvep.supabase.co
2. Allez dans l'onglet "SQL Editor"

### 2. Créer les Tables

Copiez et exécutez le contenu du fichier `database/schema.sql` dans l'éditeur SQL de Supabase.

Ce script va créer :
- **projects** : Table principale des projets audio
- **audio_analyses** : Analyses techniques des fichiers audio
- **separation_settings** : Paramètres de séparation utilisés
- **stems** : Pistes séparées générées
- **processing_results** : Résultats et historique des traitements

### 3. Vérifier les Tables

Après exécution du script, vérifiez dans l'onglet "Table Editor" que toutes les tables ont été créées :

```
✓ projects
✓ audio_analyses  
✓ separation_settings
✓ stems
✓ processing_results
```

### 4. Configuration RLS (Row Level Security)

Le script active automatiquement la sécurité au niveau des lignes (RLS) pour protéger les données des utilisateurs.

Les politiques configurées permettent :
- Aux utilisateurs de voir uniquement leurs propres projets
- L'accès public aux projets sans user_id (pour les utilisateurs non connectés)
- La protection contre l'accès non autorisé aux données

### 5. Test de la Configuration

Pour tester que tout fonctionne :

1. Lancez l'application : `npm run dev`
2. Uploadez un fichier audio
3. Vérifiez dans Supabase que le projet apparaît dans la table `projects`
4. Lancez une séparation et vérifiez que les données sont sauvegardées

## Structure des Données

### Table `projects`
- `id` : UUID unique du projet
- `name` : Nom du projet
- `original_filename` : Nom du fichier original
- `file_size` : Taille du fichier en bytes
- `duration` : Durée en secondes
- `status` : Statut ('uploaded', 'processing', 'completed', 'error')
- `user_id` : ID de l'utilisateur (optionnel)
- `created_at` / `updated_at` : Timestamps

### Table `audio_analyses`
- Données techniques du fichier audio
- Fréquence d'échantillonnage, canaux, débit, etc.
- Analyse spectrale et dynamique

### Table `separation_settings`
- Paramètres utilisés pour la séparation
- Modèle IA, qualité, options de post-traitement
- Lié au projet via `project_id`

### Table `stems`
- Pistes séparées générées
- Métadonnées audio de chaque stem
- Qualité et confiance de la séparation

### Table `processing_results`
- Historique des traitements
- Temps de traitement, statut, erreurs éventuelles

## Fonctionnalités Implémentées

✅ **Sauvegarde automatique** des projets lors de l'upload
✅ **Historique des projets** avec interface de navigation
✅ **Persistance des analyses** audio
✅ **Sauvegarde des paramètres** de séparation
✅ **Stockage des résultats** (stems et métadonnées)
✅ **Gestion des erreurs** et statuts de traitement
✅ **Sécurité RLS** pour protéger les données utilisateur

## Prochaines Étapes

Pour une application complète, vous pourriez ajouter :

1. **Authentification utilisateur** avec Supabase Auth
2. **Stockage des fichiers** avec Supabase Storage
3. **Partage de projets** entre utilisateurs
4. **Export/Import** de projets
5. **Statistiques d'utilisation**
6. **Système de favoris** et tags

## Dépannage

### Erreur de connexion
- Vérifiez que l'URL et la clé API sont correctes dans `src/lib/supabase.ts`
- Vérifiez que les politiques RLS permettent l'accès

### Tables non créées
- Exécutez le script SQL complet dans l'éditeur Supabase
- Vérifiez les erreurs dans les logs

### Données non sauvegardées
- Vérifiez la console du navigateur pour les erreurs
- Vérifiez que les politiques RLS sont correctement configurées

## Support

Pour toute question sur la configuration de la base de données, consultez :
- [Documentation Supabase](https://supabase.com/docs)
- [Guide RLS](https://supabase.com/docs/guides/auth/row-level-security)
