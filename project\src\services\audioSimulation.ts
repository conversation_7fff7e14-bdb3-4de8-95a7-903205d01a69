import { Stem } from '../types/audio'

export class AudioSimulationService {
  private static audioContext: AudioContext | null = null
  private static sourceBuffers = new Map<string, AudioBuffer>()

  // Initialiser le contexte audio
  private static getAudioContext(): AudioContext {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    }
    return this.audioContext
  }

  // Créer un fichier audio simulé pour un stem
  static async createStemAudio(stem: Stem, originalFile: File, duration: number = 30): Promise<string> {
    try {
      const audioContext = this.getAudioContext()
      
      // Créer un buffer audio simulé basé sur le type de stem
      const sampleRate = 44100
      const length = sampleRate * Math.min(duration, 30) // Limiter à 30 secondes pour la démo
      const buffer = audioContext.createBuffer(2, length, sampleRate)
      
      // Générer un audio simulé selon le type de stem
      this.generateStemAudio(buffer, stem, length)
      
      // Convertir le buffer en blob audio
      const audioBlob = await this.bufferToWav(buffer)
      return URL.createObjectURL(audioBlob)
      
    } catch (error) {
      console.error('Erreur lors de la création de l\'audio simulé:', error)
      // Fallback: utiliser le fichier original
      return URL.createObjectURL(originalFile)
    }
  }

  // Générer un audio simulé selon le type de stem
  private static generateStemAudio(buffer: AudioBuffer, stem: Stem, length: number) {
    const leftChannel = buffer.getChannelData(0)
    const rightChannel = buffer.getChannelData(1)
    
    for (let i = 0; i < length; i++) {
      const time = i / buffer.sampleRate
      let sample = 0
      
      switch (stem.type) {
        case 'vocal':
          // Simulation de voix avec harmoniques
          sample = this.generateVocalSimulation(time, stem.metadata.spectralCentroid)
          break
          
        case 'instrumental':
          // Simulation d'instrumental avec plusieurs fréquences
          sample = this.generateInstrumentalSimulation(time)
          break
          
        case 'drums':
          // Simulation de batterie avec percussions
          sample = this.generateDrumSimulation(time)
          break
          
        case 'bass':
          // Simulation de basse avec basses fréquences
          sample = this.generateBassSimulation(time)
          break
          
        default:
          // Audio générique
          sample = this.generateGenericSimulation(time)
      }
      
      // Appliquer l'enveloppe et les métadonnées du stem
      sample *= this.getAmplitudeEnvelope(time, length / buffer.sampleRate)
      sample *= stem.quality // Réduire le volume selon la qualité
      
      leftChannel[i] = sample
      rightChannel[i] = sample * 0.9 // Légère différence stéréo
    }
  }

  // Simulation de voix
  private static generateVocalSimulation(time: number, spectralCentroid: number): number {
    const baseFreq = Math.max(200, spectralCentroid / 10) // Fréquence de base
    const vibrato = Math.sin(time * 6) * 0.1 // Vibrato
    
    return (
      Math.sin(2 * Math.PI * baseFreq * time * (1 + vibrato)) * 0.3 +
      Math.sin(2 * Math.PI * baseFreq * 2 * time) * 0.1 + // Harmonique
      Math.sin(2 * Math.PI * baseFreq * 3 * time) * 0.05   // Harmonique
    ) * (0.5 + Math.random() * 0.1) // Variation naturelle
  }

  // Simulation d'instrumental
  private static generateInstrumentalSimulation(time: number): number {
    return (
      Math.sin(2 * Math.PI * 440 * time) * 0.2 +      // La
      Math.sin(2 * Math.PI * 554.37 * time) * 0.15 +  // Do#
      Math.sin(2 * Math.PI * 659.25 * time) * 0.1 +   // Mi
      Math.sin(2 * Math.PI * 880 * time) * 0.05       // La octave
    ) * (0.7 + Math.sin(time * 0.5) * 0.3) // Modulation lente
  }

  // Simulation de batterie
  private static generateDrumSimulation(time: number): number {
    const beat = Math.floor(time * 2) % 4 // 4/4 à 120 BPM
    const subBeat = (time * 2) % 1
    
    let sample = 0
    
    // Kick sur les temps 1 et 3
    if ((beat === 0 || beat === 2) && subBeat < 0.1) {
      sample += Math.sin(2 * Math.PI * 60 * time) * Math.exp(-subBeat * 20) * 0.8
    }
    
    // Snare sur les temps 2 et 4
    if ((beat === 1 || beat === 3) && subBeat < 0.05) {
      sample += (Math.random() - 0.5) * Math.exp(-subBeat * 30) * 0.6
    }
    
    // Hi-hat
    if (subBeat < 0.02) {
      sample += (Math.random() - 0.5) * Math.exp(-subBeat * 50) * 0.2
    }
    
    return sample
  }

  // Simulation de basse
  private static generateBassSimulation(time: number): number {
    const bassLine = [110, 110, 146.83, 110] // A, A, D, A
    const noteIndex = Math.floor(time * 2) % bassLine.length
    const freq = bassLine[noteIndex]
    
    return (
      Math.sin(2 * Math.PI * freq * time) * 0.4 +
      Math.sin(2 * Math.PI * freq * 2 * time) * 0.1 // Harmonique
    ) * (0.8 + Math.sin(time * 0.3) * 0.2)
  }

  // Simulation générique
  private static generateGenericSimulation(time: number): number {
    return (
      Math.sin(2 * Math.PI * 220 * time) * 0.2 +
      Math.sin(2 * Math.PI * 330 * time) * 0.15 +
      (Math.random() - 0.5) * 0.05
    )
  }

  // Enveloppe d'amplitude pour un son naturel
  private static getAmplitudeEnvelope(time: number, totalDuration: number): number {
    const fadeInTime = 0.1
    const fadeOutTime = 0.5
    
    if (time < fadeInTime) {
      return time / fadeInTime // Fade in
    } else if (time > totalDuration - fadeOutTime) {
      return (totalDuration - time) / fadeOutTime // Fade out
    }
    return 1 // Sustain
  }

  // Convertir AudioBuffer en WAV Blob
  private static async bufferToWav(buffer: AudioBuffer): Promise<Blob> {
    const length = buffer.length
    const sampleRate = buffer.sampleRate
    const numberOfChannels = buffer.numberOfChannels
    
    // Créer un ArrayBuffer pour le fichier WAV
    const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2)
    const view = new DataView(arrayBuffer)
    
    // En-tête WAV
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i))
      }
    }
    
    writeString(0, 'RIFF')
    view.setUint32(4, 36 + length * numberOfChannels * 2, true)
    writeString(8, 'WAVE')
    writeString(12, 'fmt ')
    view.setUint32(16, 16, true)
    view.setUint16(20, 1, true)
    view.setUint16(22, numberOfChannels, true)
    view.setUint32(24, sampleRate, true)
    view.setUint32(28, sampleRate * numberOfChannels * 2, true)
    view.setUint16(32, numberOfChannels * 2, true)
    view.setUint16(34, 16, true)
    writeString(36, 'data')
    view.setUint32(40, length * numberOfChannels * 2, true)
    
    // Données audio
    let offset = 44
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]))
        view.setInt16(offset, sample * 0x7FFF, true)
        offset += 2
      }
    }
    
    return new Blob([arrayBuffer], { type: 'audio/wav' })
  }

  // Nettoyer les URLs créées
  static cleanup() {
    // Les URLs seront automatiquement nettoyées par le garbage collector
    // mais on peut forcer le nettoyage si nécessaire
  }
}
