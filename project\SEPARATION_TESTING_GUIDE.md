# 🔧 Guide de Test - Séparation des Stems

Ce guide vous aide à tester et déboguer la fonctionnalité de séparation des stems.

## 🚀 Corrections Apportées

### Problèmes Identifiés et Corrigés
1. ❌ **Bouton désactivé** après première séparation (`stems.length > 0`)
2. ❌ **Logique de traitement complexe** avec timeouts imbriqués
3. ❌ **Manque de feedback** pour identifier les problèmes
4. ❌ **Pas de bouton de reset** pour relancer

### Solutions Implémentées
1. ✅ **Bouton toujours actif** (sauf pendant traitement)
2. ✅ **Logique simplifiée** avec async/await
3. ✅ **Panel de debug** avec état en temps réel
4. ✅ **Bouton reset** pour nouveau traitement
5. ✅ **Logs détaillés** dans la console

## 🧪 Comment Tester

### 1. **Ouvrir l'Application**
```bash
cd project
npm run dev
```
Aller sur http://localhost:5174

### 2. **Utiliser le Panel de Debug**
Un nouveau panel jaune apparaît avec :
- **État actuel** : Prêt/En cours
- **Nombre de stems** : Compteur en temps réel
- **Statut fichier** : Chargé/Aucun
- **Bouton "Test Séparation"** : Lance le traitement
- **Bouton "Reset"** : Remet à zéro

### 3. **Étapes de Test**

#### **Test Basique**
1. **Télécharger** un fichier audio (MP3, WAV, etc.)
2. **Vérifier** que le panel montre "✅ Chargé"
3. **Cliquer** sur "Test Séparation" ou "Lancer la séparation"
4. **Observer** la progression dans le panel
5. **Attendre** ~15 secondes
6. **Vérifier** que les stems apparaissent

#### **Test avec Console**
1. **Ouvrir** les DevTools (F12)
2. **Aller** dans l'onglet Console
3. **Lancer** la séparation
4. **Observer** les logs détaillés :
   ```
   🚀 Démarrage du traitement...
   📋 Traitement de 5 étapes...
   ⏳ Étape 1/5: Analyse spectrale
   ✅ Étape terminée: Analyse spectrale
   ...
   🎵 Génération des stems...
   ✅ Stems générés: 4 stems
   🎉 Traitement terminé avec succès !
   ```

### 4. **Vérifications à Effectuer**

#### **Pendant le Traitement**
- ✅ Bouton devient "Traitement en cours..."
- ✅ Barre de progression avance
- ✅ Étapes se succèdent dans l'ordre
- ✅ Panel debug montre "⏳ En cours"

#### **Après le Traitement**
- ✅ 4 stems apparaissent (Voix, Instrumental, Batterie, Basse)
- ✅ Bouton "Nouveau traitement" apparaît
- ✅ Panel debug montre "4 générés"
- ✅ Boutons de lecture sont verts et fonctionnels

## 🔍 Debugging

### **Si la Séparation ne Démarre Pas**

#### Vérifier dans la Console :
```javascript
// Vérifier l'état
console.log('État:', {
  isProcessing: false, // Doit être false
  selectedFile: File, // Doit être un objet File
  stems: [], // Peut être vide ou plein
})
```

#### Actions à Tenter :
1. **Recharger** la page (Ctrl+F5)
2. **Re-télécharger** un fichier
3. **Cliquer** sur "Reset" dans le panel debug
4. **Vérifier** qu'aucune erreur n'apparaît en console

### **Si le Traitement se Bloque**

#### Symptômes :
- Progression arrêtée à une étape
- Pas de logs dans la console
- Bouton reste "En cours..."

#### Solutions :
1. **Attendre** 30 secondes (timeout automatique)
2. **Cliquer** sur "Reset"
3. **Recharger** la page
4. **Vérifier** la console pour erreurs

### **Si les Stems n'Apparaissent Pas**

#### Vérifier :
```javascript
// Dans la console après traitement
console.log('Stems générés:', stems.length) // Doit être 4
```

#### Actions :
1. **Vérifier** que `generateStems()` a été appelée
2. **Chercher** les logs "🎵 Génération des stems..."
3. **Vérifier** qu'aucune erreur n'interrompt le processus

## 🎯 États Attendus

### **Avant Traitement**
```javascript
{
  isProcessing: false,
  stems: [],
  processingSteps: [],
  selectedFile: File {...}
}
```

### **Pendant Traitement**
```javascript
{
  isProcessing: true,
  stems: [],
  processingSteps: [
    { id: 'analysis', status: 'processing', progress: 45 },
    { id: 'preprocessing', status: 'pending', progress: 0 },
    // ...
  ]
}
```

### **Après Traitement**
```javascript
{
  isProcessing: false,
  stems: [
    { id: 'vocal', name: 'Voix', type: 'vocal', ... },
    { id: 'instrumental', name: 'Instrumental', ... },
    { id: 'drums', name: 'Batterie', ... },
    { id: 'bass', name: 'Basse', ... }
  ],
  processingSteps: [
    { id: 'analysis', status: 'completed', progress: 100 },
    // ... tous completed
  ]
}
```

## 🛠️ Fonctionnalités du Panel Debug

### **Indicateurs Visuels**
- 🟢 **Vert** : État OK, prêt
- 🔵 **Bleu** : Traitement en cours
- 🔴 **Rouge** : Erreur ou manquant
- 🟡 **Jaune** : Attention/Warning

### **Boutons d'Action**
- **"Test Séparation"** : Lance le traitement avec logs détaillés
- **"Reset"** : Remet tout à zéro pour un nouveau test
- **État temps réel** : Mise à jour automatique

### **Informations Affichées**
- **État du traitement** : Prêt/En cours
- **Nombre de stems** : Compteur en temps réel
- **Statut du fichier** : Présence d'un fichier audio
- **Étapes de traitement** : Progression détaillée

## ✅ Checklist de Test

- [ ] Panel debug visible et fonctionnel
- [ ] Téléchargement de fichier fonctionne
- [ ] Bouton "Test Séparation" cliquable
- [ ] Progression visible pendant traitement
- [ ] Logs détaillés dans la console
- [ ] 4 stems générés après traitement
- [ ] Boutons de lecture audio fonctionnels
- [ ] Bouton "Reset" remet à zéro
- [ ] Possibilité de relancer un traitement

## 🎉 Résultat Attendu

Après avoir suivi ce guide, vous devriez pouvoir :
1. **Télécharger** un fichier audio
2. **Lancer** la séparation via le panel debug
3. **Voir** la progression en temps réel
4. **Obtenir** 4 stems fonctionnels
5. **Écouter** chaque stem individuellement
6. **Relancer** un nouveau traitement

**Si tout fonctionne, la séparation des stems est opérationnelle ! 🎵**
