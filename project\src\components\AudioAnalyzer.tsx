import React, { useEffect, useState, useCallback } from 'react';
import { BarChart3, Activity, Volume2, Zap } from 'lucide-react';
import { AudioAnalysis } from '../types/audio';
import { LoadingSpinner } from './LoadingSpinner';

interface AudioAnalyzerProps {
  audioFile: File | null;
  onAnalysisComplete: (analysis: AudioAnalysis) => void;
}

export const AudioAnalyzer: React.FC<AudioAnalyzerProps> = ({ 
  audioFile, 
  onAnalysisComplete 
}) => {
  const [analysis, setAnalysis] = useState<AudioAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  useEffect(() => {
    if (!audioFile) return;

    const analyzeAudio = async () => {
      setIsAnalyzing(true);
      
      try {
        const arrayBuffer = await audioFile.arrayBuffer();
        const audioContext = new AudioContext();
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        
        // Analyse basique
        const channelData = audioBuffer.getChannelData(0);
        const sampleRate = audioBuffer.sampleRate;
        const duration = audioBuffer.duration;
        const channels = audioBuffer.numberOfChannels;
        
        // Calcul du niveau RMS
        let sumSquares = 0;
        for (let i = 0; i < channelData.length; i++) {
          sumSquares += channelData[i] * channelData[i];
        }
        const rms = Math.sqrt(sumSquares / channelData.length);
        const loudness = 20 * Math.log10(rms);
        
        // Calcul de la plage dynamique (approximation)
        const sortedSamples = Array.from(channelData).sort((a, b) => Math.abs(b) - Math.abs(a));
        const peak = Math.abs(sortedSamples[0]);
        const rms90 = Math.sqrt(
          sortedSamples.slice(0, Math.floor(sortedSamples.length * 0.1))
            .reduce((sum, sample) => sum + sample * sample, 0) / 
          Math.floor(sortedSamples.length * 0.1)
        );
        const dynamicRange = 20 * Math.log10(peak / rms90);
        
        // Calcul du centroïde spectral (approximation)
        const fftSize = 2048;
        const analyser = audioContext.createAnalyser();
        analyser.fftSize = fftSize;
        const frequencyData = new Uint8Array(analyser.frequencyBinCount);
        
        // Simulation du centroïde spectral
        let weightedSum = 0;
        let magnitudeSum = 0;
        for (let i = 0; i < frequencyData.length; i++) {
          const frequency = (i * sampleRate) / (2 * frequencyData.length);
          const magnitude = Math.random() * 255; // Simulation
          weightedSum += frequency * magnitude;
          magnitudeSum += magnitude;
        }
        const spectralCentroid = magnitudeSum > 0 ? weightedSum / magnitudeSum : 0;
        
        const analysisResult: AudioAnalysis = {
          sampleRate,
          duration,
          channels,
          bitRate: Math.round((arrayBuffer.byteLength * 8) / duration),
          format: audioFile.type.split('/')[1].toUpperCase(),
          loudness,
          dynamicRange: Math.max(0, dynamicRange),
          spectralCentroid
        };
        
        setAnalysis(analysisResult);
        onAnalysisComplete(analysisResult);
        audioContext.close();
        
      } catch (error) {
        console.error('Erreur lors de l\'analyse audio:', error);
      } finally {
        setIsAnalyzing(false);
      }
    };

    analyzeAudio();
  }, [audioFile, onAnalysisComplete]);

  if (!audioFile) return null;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
      <div className="flex items-center space-x-3 mb-4">
        <BarChart3 className="w-5 h-5 text-purple-500" />
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
          Analyse Audio
        </h3>
        {isAnalyzing && (
          <div className="flex items-center space-x-2 text-sm text-purple-600">
            <Activity className="w-4 h-4 animate-pulse" />
            <span>Analyse en cours...</span>
          </div>
        )}
      </div>

      {analysis && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Volume2 className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                Format
              </span>
            </div>
            <div className="text-lg font-bold text-gray-800 dark:text-white">
              {analysis.format}
            </div>
            <div className="text-xs text-gray-500">
              {analysis.channels} ch • {(analysis.sampleRate / 1000).toFixed(1)}kHz
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Activity className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                Durée
              </span>
            </div>
            <div className="text-lg font-bold text-gray-800 dark:text-white">
              {Math.floor(analysis.duration / 60)}:{(analysis.duration % 60).toFixed(0).padStart(2, '0')}
            </div>
            <div className="text-xs text-gray-500">
              {(analysis.bitRate / 1000).toFixed(0)} kbps
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Zap className="w-4 h-4 text-yellow-500" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                Loudness
              </span>
            </div>
            <div className="text-lg font-bold text-gray-800 dark:text-white">
              {analysis.loudness.toFixed(1)} dB
            </div>
            <div className="text-xs text-gray-500">
              LUFS approximé
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <BarChart3 className="w-4 h-4 text-purple-500" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                Plage Dyn.
              </span>
            </div>
            <div className="text-lg font-bold text-gray-800 dark:text-white">
              {analysis.dynamicRange.toFixed(1)} dB
            </div>
            <div className="text-xs text-gray-500">
              Centroïde: {(analysis.spectralCentroid / 1000).toFixed(1)}kHz
            </div>
          </div>
        </div>
      )}

      {analysis && (
        <div className="mt-4 p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
          <h4 className="font-medium text-gray-800 dark:text-white mb-2">
            Recommandations de traitement
          </h4>
          <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
            {analysis.dynamicRange > 15 && (
              <p>✓ Excellente plage dynamique - Idéal pour séparation haute qualité</p>
            )}
            {analysis.loudness > -12 && (
              <p>⚠️ Audio très fort - Normalisation recommandée</p>
            )}
            {analysis.sampleRate >= 44100 && (
              <p>✓ Fréquence d'échantillonnage optimale</p>
            )}
            {analysis.spectralCentroid > 2000 && (
              <p>✓ Contenu spectral riche - Bonne séparation attendue</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};