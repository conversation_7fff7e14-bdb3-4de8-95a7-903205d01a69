import React from 'react'
import { <PERSON>, <PERSON>, FileAudio, Trash2, Play } from 'lucide-react'
import { DatabaseProject } from '../lib/supabase'

interface ProjectHistoryProps {
  projects: DatabaseProject[]
  onSelectProject: (project: DatabaseProject) => void
  onDeleteProject?: (projectId: string) => void
  loading: boolean
}

export const ProjectHistory: React.FC<ProjectHistoryProps> = ({
  projects,
  onSelectProject,
  onDeleteProject,
  loading
}) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: DatabaseProject['status']): string => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100'
      case 'processing':
        return 'text-blue-600 bg-blue-100'
      case 'error':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: DatabaseProject['status']): string => {
    switch (status) {
      case 'completed':
        return 'Terminé'
      case 'processing':
        return 'En cours'
      case 'error':
        return 'Erreur'
      case 'uploaded':
        return 'Téléchargé'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 mb-4">
          <Clock className="w-5 h-5 text-gray-500" />
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
            Historique des projets
          </h3>
        </div>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (projects.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 mb-4">
          <Clock className="w-5 h-5 text-gray-500" />
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
            Historique des projets
          </h3>
        </div>
        <div className="text-center py-8">
          <Music className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500 dark:text-gray-400">
            Aucun projet trouvé. Commencez par télécharger un fichier audio.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex items-center space-x-3 mb-4">
        <Clock className="w-5 h-5 text-gray-500" />
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
          Historique des projets
        </h3>
        <span className="text-sm text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
          {projects.length}
        </span>
      </div>

      <div className="space-y-3 max-h-96 overflow-y-auto">
        {projects.map((project) => (
          <div
            key={project.id}
            className="group p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-purple-300 dark:hover:border-purple-500 transition-all cursor-pointer"
            onClick={() => onSelectProject(project)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-2">
                  <FileAudio className="w-4 h-4 text-purple-500 flex-shrink-0" />
                  <h4 className="font-medium text-gray-800 dark:text-white truncate">
                    {project.name}
                  </h4>
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(project.status)}`}>
                    {getStatusText(project.status)}
                  </span>
                </div>
                
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate mb-2">
                  {project.original_filename}
                </p>
                
                <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                  <span>{formatFileSize(project.file_size)}</span>
                  {project.duration > 0 && (
                    <span>{formatDuration(project.duration)}</span>
                  )}
                  <span>{formatDate(project.created_at)}</span>
                </div>
              </div>

              <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onSelectProject(project)
                  }}
                  className="p-2 text-purple-600 hover:bg-purple-100 dark:hover:bg-purple-900/20 rounded-lg transition-colors"
                  title="Ouvrir le projet"
                >
                  <Play className="w-4 h-4" />
                </button>
                
                {onDeleteProject && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      if (window.confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {
                        onDeleteProject(project.id)
                      }
                    }}
                    className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    title="Supprimer le projet"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
