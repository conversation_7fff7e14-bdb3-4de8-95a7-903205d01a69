import { AudioAnalysis } from '../types/audio'

export class FastAudioAnalyzer {
  private static cache = new Map<string, AudioAnalysis>()
  
  // Analyse ultra-rapide basée sur les métadonnées du fichier
  static async analyzeFile(file: File): Promise<AudioAnalysis> {
    // Vérifier le cache d'abord
    const cacheKey = `${file.name}_${file.size}_${file.lastModified}`
    const cached = this.cache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      // Analyse rapide basée sur la taille et le type
      const analysis = await this.quickAnalysis(file)
      
      // Mettre en cache
      this.cache.set(cacheKey, analysis)
      
      return analysis
    } catch (error) {
      console.error('Erreur analyse rapide:', error)
      return this.fallbackAnalysis(file)
    }
  }

  private static async quickAnalysis(file: File): Promise<AudioAnalysis> {
    const fileType = file.type.toLowerCase()
    const fileSize = file.size
    
    // Estimation du bitrate selon le type et la taille
    let estimatedBitRate = 128000 // 128 kbps par défaut
    let estimatedDuration = 180 // 3 minutes par défaut
    
    // Analyse basée sur le type de fichier
    if (fileType.includes('mp3')) {
      // Estimation pour MP3
      if (fileSize < 2 * 1024 * 1024) { // < 2MB
        estimatedBitRate = 96000 // 96k
      } else if (fileSize < 4 * 1024 * 1024) { // < 4MB
        estimatedBitRate = 128000 // 128k
      } else if (fileSize < 8 * 1024 * 1024) { // < 8MB
        estimatedBitRate = 192000 // 192k
      } else {
        estimatedBitRate = 320000 // 320k
      }
    } else if (fileType.includes('wav')) {
      estimatedBitRate = 1411000 // 1411k (16-bit, 44.1kHz, stereo)
    } else if (fileType.includes('flac')) {
      estimatedBitRate = 1000000 // ~1000k (compression variable)
    } else if (fileType.includes('m4a') || fileType.includes('aac')) {
      estimatedBitRate = 256000 // 256k AAC
    } else if (fileType.includes('ogg')) {
      estimatedBitRate = 192000 // 192k Vorbis
    }
    
    // Calcul de la durée estimée
    estimatedDuration = (fileSize * 8) / estimatedBitRate
    
    // Pour les fichiers très petits (< 1MB), analyse plus détaillée
    if (fileSize < 1024 * 1024) {
      try {
        return await this.detailedQuickAnalysis(file)
      } catch {
        // Fallback si l'analyse détaillée échoue
      }
    }
    
    // Valeurs typiques selon le format
    const formatDefaults = this.getFormatDefaults(fileType)
    
    return {
      sampleRate: 44100,
      duration: Math.max(10, estimatedDuration), // Minimum 10 secondes
      channels: 2,
      bitRate: estimatedBitRate,
      format: this.getFormatName(fileType),
      loudness: formatDefaults.loudness,
      dynamicRange: formatDefaults.dynamicRange,
      spectralCentroid: formatDefaults.spectralCentroid
    }
  }

  private static async detailedQuickAnalysis(file: File): Promise<AudioAnalysis> {
    // Analyse rapide pour les petits fichiers seulement
    const arrayBuffer = await file.arrayBuffer()
    
    // Utiliser un contexte audio avec une durée limitée
    const audioContext = new AudioContext()
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
    
    const duration = audioBuffer.duration
    const sampleRate = audioBuffer.sampleRate
    const channels = audioBuffer.numberOfChannels
    
    // Échantillonnage très agressif (1 sur 10000)
    const channelData = audioBuffer.getChannelData(0)
    const step = Math.max(10000, Math.floor(channelData.length / 100))
    
    let sumSquares = 0
    let peak = 0
    let sampleCount = 0
    
    for (let i = 0; i < channelData.length; i += step) {
      const sample = channelData[i]
      const absSample = Math.abs(sample)
      sumSquares += sample * sample
      if (absSample > peak) peak = absSample
      sampleCount++
    }
    
    const rms = Math.sqrt(sumSquares / sampleCount)
    const loudness = rms > 0 ? Math.max(-60, 20 * Math.log10(rms)) : -60
    const dynamicRange = (peak > 0 && rms > 0) ? Math.min(60, 20 * Math.log10(peak / rms)) : 25
    
    audioContext.close()
    
    return {
      sampleRate,
      duration,
      channels,
      bitRate: Math.round((arrayBuffer.byteLength * 8) / duration),
      format: this.getFormatName(file.type),
      loudness,
      dynamicRange,
      spectralCentroid: this.estimateSpectralCentroid(file.type, sampleRate)
    }
  }

  private static getFormatDefaults(fileType: string) {
    if (fileType.includes('mp3')) {
      return { loudness: -23, dynamicRange: 20, spectralCentroid: 1800 }
    } else if (fileType.includes('wav')) {
      return { loudness: -18, dynamicRange: 30, spectralCentroid: 2200 }
    } else if (fileType.includes('flac')) {
      return { loudness: -16, dynamicRange: 35, spectralCentroid: 2400 }
    } else if (fileType.includes('m4a') || fileType.includes('aac')) {
      return { loudness: -20, dynamicRange: 25, spectralCentroid: 2000 }
    } else if (fileType.includes('ogg')) {
      return { loudness: -21, dynamicRange: 22, spectralCentroid: 1900 }
    }
    
    // Valeurs par défaut
    return { loudness: -20, dynamicRange: 25, spectralCentroid: 2000 }
  }

  private static getFormatName(fileType: string): string {
    const format = fileType.split('/')[1]?.toUpperCase()
    if (!format) return 'AUDIO'
    
    // Normaliser les noms de format
    if (format === 'MPEG') return 'MP3'
    if (format === 'X-M4A') return 'M4A'
    if (format === 'OGG') return 'OGG'
    
    return format
  }

  private static estimateSpectralCentroid(fileType: string, sampleRate: number): number {
    let base = 2000
    
    // Ajustement selon le format
    if (fileType.includes('mp3')) base = 1800
    else if (fileType.includes('wav')) base = 2200
    else if (fileType.includes('flac')) base = 2400
    
    // Ajustement selon la fréquence d'échantillonnage
    if (sampleRate > 48000) base += 200
    else if (sampleRate < 44100) base -= 200
    
    return Math.max(500, Math.min(8000, base))
  }

  private static fallbackAnalysis(file: File): AudioAnalysis {
    // Analyse de secours ultra-simple
    const estimatedDuration = Math.max(30, file.size / (128000 / 8)) // Estimation 128kbps
    
    return {
      sampleRate: 44100,
      duration: estimatedDuration,
      channels: 2,
      bitRate: 128000,
      format: this.getFormatName(file.type),
      loudness: -20,
      dynamicRange: 25,
      spectralCentroid: 2000
    }
  }

  // Nettoyer le cache périodiquement
  static clearCache(): void {
    this.cache.clear()
  }

  static getCacheSize(): number {
    return this.cache.size
  }
}
