import React from 'react'
import { Bug, Play, RotateCcw } from 'lucide-react'

interface DebugPanelProps {
  isProcessing: boolean
  stems: any[]
  processingSteps: any[]
  onStartProcessing: () => void
  onReset: () => void
  selectedFile: File | null
}

export const DebugPanel: React.FC<DebugPanelProps> = ({
  isProcessing,
  stems,
  processingSteps,
  onStartProcessing,
  onReset,
  selectedFile
}) => {
  const handleTestSeparation = () => {
    console.log('🧪 Test de séparation forcé...')
    console.log('État actuel:', {
      isProcessing,
      stemsCount: stems.length,
      stepsCount: processingSteps.length,
      hasFile: !!selectedFile
    })
    
    if (!selectedFile) {
      alert('⚠️ Veuillez d\'abord sélectionner un fichier audio')
      return
    }
    
    onStartProcessing()
  }

  return (
    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-xl p-4 mb-6">
      <div className="flex items-center space-x-3 mb-4">
        <Bug className="w-5 h-5 text-yellow-600" />
        <h4 className="font-medium text-yellow-800 dark:text-yellow-200">
          🔧 Panel de Debug - Séparation Audio
        </h4>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">État</div>
          <div className={`text-lg font-bold ${isProcessing ? 'text-blue-600' : 'text-green-600'}`}>
            {isProcessing ? '⏳ En cours' : '✅ Prêt'}
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Stems</div>
          <div className="text-lg font-bold text-purple-600">
            {stems.length} générés
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Fichier</div>
          <div className={`text-lg font-bold ${selectedFile ? 'text-green-600' : 'text-red-600'}`}>
            {selectedFile ? '✅ Chargé' : '❌ Aucun'}
          </div>
        </div>
      </div>
      
      <div className="flex items-center space-x-3">
        <button
          onClick={handleTestSeparation}
          disabled={isProcessing || !selectedFile}
          className={`
            flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all
            ${isProcessing || !selectedFile
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-500 text-white hover:bg-blue-600'
            }
          `}
        >
          <Play className="w-4 h-4" />
          <span>Test Séparation</span>
        </button>
        
        <button
          onClick={onReset}
          className="flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all"
        >
          <RotateCcw className="w-4 h-4" />
          <span>Reset</span>
        </button>
      </div>
      
      {processingSteps.length > 0 && (
        <div className="mt-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Étapes de traitement:
          </div>
          <div className="space-y-1">
            {processingSteps.map((step, index) => (
              <div key={step.id} className="flex items-center space-x-2 text-xs">
                <div className={`w-2 h-2 rounded-full ${
                  step.status === 'completed' ? 'bg-green-500' :
                  step.status === 'processing' ? 'bg-blue-500' :
                  'bg-gray-300'
                }`} />
                <span className="text-gray-600 dark:text-gray-400">
                  {index + 1}. {step.name} ({step.progress}%)
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="mt-4 text-xs text-yellow-700 dark:text-yellow-300">
        💡 <strong>Instructions:</strong> 
        1. Sélectionnez un fichier audio
        2. Cliquez sur "Test Séparation" 
        3. Vérifiez la console (F12) pour les logs détaillés
        4. Les stems devraient apparaître après ~15 secondes
      </div>
    </div>
  )
}
