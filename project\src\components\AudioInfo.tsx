import React from 'react'
import { Info, Volume2, Music } from 'lucide-react'

export const AudioInfo: React.FC = () => {
  return (
    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-xl p-4 mb-6">
      <div className="flex items-start space-x-3">
        <Info className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
        <div className="flex-1">
          <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
            🎵 Audio de Démonstration
          </h4>
          <div className="text-sm text-blue-700 dark:text-blue-300 space-y-2">
            <p>
              Les stems que vous entendez sont des <strong>simulations audio générées</strong> pour démontrer les fonctionnalités de l'interface.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
              <div className="flex items-center space-x-2">
                <Volume2 className="w-4 h-4 text-amber-500" />
                <span><strong>Voix</strong> : Simulation de voix avec harmoniques</span>
              </div>
              <div className="flex items-center space-x-2">
                <Music className="w-4 h-4 text-green-500" />
                <span><strong>Instrumental</strong> : Accords et mélodies</span>
              </div>
              <div className="flex items-center space-x-2">
                <Volume2 className="w-4 h-4 text-red-500" />
                <span><strong>Batterie</strong> : Rythme 4/4 avec kick et snare</span>
              </div>
              <div className="flex items-center space-x-2">
                <Music className="w-4 h-4 text-purple-500" />
                <span><strong>Basse</strong> : Ligne de basse en boucle</span>
              </div>
            </div>
            <div className="mt-3 p-3 bg-blue-100 dark:bg-blue-800/30 rounded-lg">
              <p className="text-xs">
                <strong>Note :</strong> Dans une application réelle, ces stems seraient générés par des algorithmes d'IA de séparation audio comme DEMUCS ou Spleeter, et vous entendriez les véritables pistes séparées de votre fichier audio.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
