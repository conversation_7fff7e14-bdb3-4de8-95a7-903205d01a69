import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Download, Volume2, VolumeX, BarChart3, Headphones, Award } from 'lucide-react';
import { Stem } from '../types/audio';
import { AudioSimulationService } from '../services/audioSimulation';
import { AudioInfo } from './AudioInfo';


interface StemPlayerProps {
  stems: Stem[];
  originalFile: File | null;
}

export const StemPlayer: React.FC<StemPlayerProps> = ({ stems, originalFile }) => {
  const [playingStems, setPlayingStems] = useState<Set<string>>(new Set());
  const [volumes, setVolumes] = useState<Record<string, number>>({});
  const [isMuted, setIsMuted] = useState<Record<string, boolean>>({});
  const [soloStem, setSoloStem] = useState<string | null>(null);
  const [audioUrls, setAudioUrls] = useState<Record<string, string>>({});
  const [isGeneratingAudio, setIsGeneratingAudio] = useState<Record<string, boolean>>({});
  const audioRefs = useRef<Record<string, HTMLAudioElement>>({});

  useEffect(() => {
    // Initialize volumes and mute states
    const initialVolumes: Record<string, number> = {};
    const initialMuted: Record<string, boolean> = {};
    const initialGenerating: Record<string, boolean> = {};

    stems.forEach(stem => {
      initialVolumes[stem.id] = 0.8;
      initialMuted[stem.id] = false;
      initialGenerating[stem.id] = false;
    });

    setVolumes(initialVolumes);
    setIsMuted(initialMuted);
    setIsGeneratingAudio(initialGenerating);

    // Générer les fichiers audio simulés
    generateAudioForStems();
  }, [stems]);

  const generateAudioForStems = async () => {
    if (!originalFile) return;

    for (const stem of stems) {
      if (!audioUrls[stem.id]) {
        setIsGeneratingAudio(prev => ({ ...prev, [stem.id]: true }));

        try {
          const audioUrl = await AudioSimulationService.createStemAudio(stem, originalFile, 30);
          setAudioUrls(prev => ({ ...prev, [stem.id]: audioUrl }));

          // Configurer l'élément audio
          const audio = audioRefs.current[stem.id];
          if (audio) {
            audio.src = audioUrl;
            audio.volume = volumes[stem.id] || 0.8;
            audio.muted = isMuted[stem.id] || false;
          }
        } catch (error) {
          console.error(`Erreur lors de la génération audio pour ${stem.name}:`, error);
        } finally {
          setIsGeneratingAudio(prev => ({ ...prev, [stem.id]: false }));
        }
      }
    }
  };

  const getQualityColor = (quality: number) => {
    if (quality >= 0.9) return 'text-green-500';
    if (quality >= 0.7) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getQualityLabel = (quality: number) => {
    if (quality >= 0.95) return 'Excellent';
    if (quality >= 0.85) return 'Très bon';
    if (quality >= 0.7) return 'Bon';
    if (quality >= 0.5) return 'Moyen';
    return 'Faible';
  };

  const togglePlay = async (stemId: string) => {
    const audio = audioRefs.current[stemId];
    if (!audio || !audioUrls[stemId]) {
      console.log('Audio non disponible pour', stemId);
      return;
    }

    try {
      if (playingStems.has(stemId)) {
        audio.pause();
        setPlayingStems(prev => {
          const newSet = new Set(prev);
          newSet.delete(stemId);
          return newSet;
        });
      } else {
        // Gérer le mode solo
        if (soloStem && soloStem !== stemId) {
          // Arrêter les autres stems si en mode solo
          Object.keys(audioRefs.current).forEach(id => {
            if (id !== stemId && playingStems.has(id)) {
              audioRefs.current[id]?.pause();
            }
          });
          setPlayingStems(new Set([stemId]));
        }

        await audio.play();
        setPlayingStems(prev => new Set(prev).add(stemId));
      }
    } catch (error) {
      console.error('Erreur lors de la lecture:', error);
    }
  };

  const handleVolumeChange = (stemId: string, volume: number) => {
    setVolumes(prev => ({ ...prev, [stemId]: volume }));
    const audio = audioRefs.current[stemId];
    if (audio) {
      audio.volume = volume;
    }
  };

  const toggleMute = (stemId: string) => {
    setIsMuted(prev => ({ ...prev, [stemId]: !prev[stemId] }));
    const audio = audioRefs.current[stemId];
    if (audio) {
      audio.muted = !isMuted[stemId];
    }
  };

  const toggleSolo = (stemId: string) => {
    if (soloStem === stemId) {
      setSoloStem(null);
    } else {
      setSoloStem(stemId);
    }
  };

  const downloadStem = (stem: Stem) => {
    // Télécharger le fichier audio simulé généré
    const audioUrl = audioUrls[stem.id];
    if (audioUrl) {
      const link = document.createElement('a');
      link.href = audioUrl;
      link.download = `${originalFile?.name.split('.')[0] || 'audio'}_${stem.name.toLowerCase()}.wav`;
      link.click();
    } else if (originalFile) {
      // Fallback: télécharger le fichier original
      const link = document.createElement('a');
      link.href = URL.createObjectURL(originalFile);
      link.download = `${originalFile.name.split('.')[0]}_${stem.name.toLowerCase()}.wav`;
      link.click();
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-6">
        Stems Séparés ({stems.length})
      </h3>

      <AudioInfo />
      
      {stems.map((stem) => (
        <div
          key={stem.id}
          className={`bg-white dark:bg-gray-800 rounded-lg p-6 border shadow-sm transition-all ${
            soloStem === stem.id 
              ? 'border-purple-400 ring-2 ring-purple-200 dark:ring-purple-800' 
              : 'border-gray-200 dark:border-gray-700'
          }`}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div
                className={`w-4 h-4 rounded-full`}
                style={{ backgroundColor: stem.color }}
              />
              <h4 className="font-semibold text-gray-800 dark:text-white">
                {stem.name}
              </h4>
              <div className="flex items-center space-x-2">
                <div className={`flex items-center space-x-1 ${getQualityColor(stem.quality)}`}>
                  <Award className="w-3 h-3" />
                  <span className="text-xs font-medium">
                    {getQualityLabel(stem.quality)}
                  </span>
                </div>
                <div className="text-xs text-gray-500">
                  {Math.round(stem.confidence * 100)}% confiance
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => toggleSolo(stem.id)}
                className={`p-2 rounded transition-colors ${
                  soloStem === stem.id
                    ? 'bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-400'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
                title="Mode Solo"
              >
                <Headphones className="w-4 h-4" />
              </button>
              
              <button
                onClick={() => toggleMute(stem.id)}
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              >
                {isMuted[stem.id] ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </button>
              
              <button
                onClick={() => downloadStem(stem)}
                className="p-2 text-blue-500 hover:text-blue-700 transition-colors"
                title="Télécharger"
              >
                <Download className="w-4 h-4" />
              </button>
              
              <button
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                title="Analyser le spectre"
              >
                <BarChart3 className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Quality and Metadata */}
          <div className="flex items-center space-x-4 mb-4 text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <span>Peak:</span>
              <span className="font-mono">{stem.metadata.peakLevel.toFixed(1)} dB</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>RMS:</span>
              <span className="font-mono">{stem.metadata.rmsLevel.toFixed(1)} dB</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>DR:</span>
              <span className="font-mono">{stem.metadata.dynamicRange.toFixed(1)} dB</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>Centroïde:</span>
              <span className="font-mono">{(stem.metadata.spectralCentroid / 1000).toFixed(1)} kHz</span>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => togglePlay(stem.id)}
              disabled={isGeneratingAudio[stem.id] || !audioUrls[stem.id]}
              className={`
                flex items-center justify-center w-12 h-12 rounded-full transition-all
                ${isGeneratingAudio[stem.id]
                  ? 'bg-yellow-500 text-white cursor-wait'
                  : !audioUrls[stem.id]
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : playingStems.has(stem.id)
                  ? 'bg-red-500 hover:bg-red-600 text-white'
                  : 'bg-green-500 hover:bg-green-600 text-white'
                }
              `}
              title={
                isGeneratingAudio[stem.id]
                  ? 'Génération audio en cours...'
                  : !audioUrls[stem.id]
                  ? 'Audio non disponible'
                  : playingStems.has(stem.id)
                  ? 'Pause'
                  : 'Lecture'
              }
            >
              {isGeneratingAudio[stem.id] ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : playingStems.has(stem.id) ? (
                <Pause className="w-5 h-5" />
              ) : (
                <Play className="w-5 h-5 ml-0.5" />
              )}
            </button>

            <div className="flex-1">
              <div className="flex items-center space-x-3">
                <Volume2 className="w-4 h-4 text-gray-500" />
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={volumes[stem.id] || 0.8}
                  onChange={(e) => handleVolumeChange(stem.id, parseFloat(e.target.value))}
                  className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
                <span className="text-sm text-gray-500 w-8">
                  {Math.round((volumes[stem.id] || 0.8) * 100)}
                </span>
              </div>
            </div>
          </div>

          {/* Enhanced waveform with spectral data */}
          <div className="mt-4 h-20 bg-gray-100 dark:bg-gray-700 rounded flex items-end justify-center space-x-1 p-2 relative overflow-hidden">
            {stem.waveformData ? stem.waveformData.map((amplitude, i) => (
              <div
                key={i}
                className="rounded-t transition-all duration-200"
                style={{
                  height: `${amplitude * 80 + 10}%`,
                  width: `${100 / stem.waveformData!.length}%`,
                  backgroundColor: playingStems.has(stem.id) 
                    ? stem.color 
                    : soloStem === stem.id 
                    ? stem.color + '80'
                    : '#9CA3AF'
                }}
              />
            )) : (
              // Fallback simulated waveform
              Array.from({ length: 60 }, (_, i) => (
                <div
                  key={i}
                  className="bg-gray-300 dark:bg-gray-600 rounded-t transition-all duration-200"
                  style={{
                    height: `${Math.random() * 70 + 15}%`,
                    width: '2px',
                    backgroundColor: playingStems.has(stem.id) ? stem.color : undefined
                  }}
                />
              ))
            )}
            
            {/* Quality indicator overlay */}
            <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
              Q: {Math.round(stem.quality * 100)}%
            </div>
          </div>

          {/* Hidden audio element for actual playback */}
          <audio
            ref={(el) => {
              if (el) audioRefs.current[stem.id] = el;
            }}
            onEnded={() => setPlayingStems(prev => {
              const newSet = new Set(prev);
              newSet.delete(stem.id);
              return newSet;
            })}
          />
        </div>
      ))}
    </div>
  );
};