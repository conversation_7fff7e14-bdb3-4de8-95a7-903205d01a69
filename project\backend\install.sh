#!/bin/bash

echo "🔧 Installation du backend DEMUCS..."

# Créer un environnement virtuel Python
echo "📦 Création de l'environnement virtuel..."
python3 -m venv venv

# Activer l'environnement virtuel
echo "🔄 Activation de l'environnement virtuel..."
source venv/bin/activate

# Installer les dépendances
echo "📥 Installation des dépendances..."
pip install --upgrade pip
pip install -r requirements.txt

echo "✅ Installation terminée !"
echo ""
echo "Pour démarrer le serveur :"
echo "  cd backend"
echo "  source venv/bin/activate"
echo "  python app.py"
echo ""
echo "Le serveur sera disponible sur http://localhost:5000"
