@echo off

echo 🚀 Démarrage du serveur DEMUCS...

REM Vérifier si l'environnement virtuel existe
if not exist "venv" (
    echo ❌ Environnement virtuel non trouvé. Exécutez d'abord install.bat
    pause
    exit /b 1
)

REM Activer l'environnement virtuel
echo 🔄 Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

REM Vérifier si DEMUCS est installé
python -c "import demucs" 2>nul
if errorlevel 1 (
    echo ❌ DEMUCS non installé. Exécutez d'abord install.bat
    pause
    exit /b 1
)

echo ✅ Démarrage du serveur sur http://localhost:5000
echo 📱 Interface web disponible sur http://localhost:5174
echo 🛑 Appuyez sur Ctrl+C pour arrêter
echo.

REM Démarrer le serveur
python app.py

pause
