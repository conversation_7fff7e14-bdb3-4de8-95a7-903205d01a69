@echo off

echo 🔧 Installation du backend DEMUCS...

REM Créer un environnement virtuel Python
echo 📦 Création de l'environnement virtuel...
python -m venv venv

REM Activer l'environnement virtuel
echo 🔄 Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

REM Installer les dépendances
echo 📥 Installation des dépendances...
python -m pip install --upgrade pip
pip install -r requirements.txt

echo ✅ Installation terminée !
echo.
echo Pour démarrer le serveur :
echo   cd backend
echo   venv\Scripts\activate.bat
echo   python app.py
echo.
echo Le serveur sera disponible sur http://localhost:5000

pause
