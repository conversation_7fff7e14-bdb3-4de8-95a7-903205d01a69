// Configuration des performances de l'application

export const PERFORMANCE_CONFIG = {
  // Cache
  CACHE_DURATION: 30000, // 30 secondes
  MAX_CACHE_SIZE: 100, // Nombre maximum d'entrées en cache
  
  // Base de données
  DB_QUERY_TIMEOUT: 10000, // 10 secondes
  MAX_PROJECTS_PER_PAGE: 50,
  ENABLE_DB_CACHE: true,
  
  // Analyse audio
  AUDIO_ANALYSIS_TIMEOUT: 30000, // 30 secondes
  USE_WEB_WORKERS: true,
  AUDIO_SAMPLE_RATE: 44100,
  FFT_SIZE: 2048,
  
  // Interface utilisateur
  DEBOUNCE_DELAY: 500, // 500ms pour les sauvegardes
  SKELETON_COUNT: 3, // Nombre de skeletons à afficher
  VIRTUAL_LIST_THRESHOLD: 20, // Seuil pour activer la virtualisation
  
  // Optimisations React
  ENABLE_MEMO: true,
  ENABLE_CALLBACK_OPTIMIZATION: true,
  
  // Monitoring
  ENABLE_PERFORMANCE_MONITORING: process.env.NODE_ENV === 'development',
  LOG_SLOW_OPERATIONS: true,
  SLOW_OPERATION_THRESHOLD: 1000 // 1 seconde
}

// Utilitaires de performance
export class PerformanceMonitor {
  private static timers = new Map<string, number>()
  
  static startTimer(label: string): void {
    if (PERFORMANCE_CONFIG.ENABLE_PERFORMANCE_MONITORING) {
      this.timers.set(label, performance.now())
    }
  }
  
  static endTimer(label: string): number {
    if (!PERFORMANCE_CONFIG.ENABLE_PERFORMANCE_MONITORING) return 0
    
    const startTime = this.timers.get(label)
    if (!startTime) return 0
    
    const duration = performance.now() - startTime
    this.timers.delete(label)
    
    if (PERFORMANCE_CONFIG.LOG_SLOW_OPERATIONS && duration > PERFORMANCE_CONFIG.SLOW_OPERATION_THRESHOLD) {
      console.warn(`⚠️ Opération lente détectée: ${label} (${duration.toFixed(2)}ms)`)
    }
    
    return duration
  }
  
  static measureAsync<T>(label: string, operation: () => Promise<T>): Promise<T> {
    return new Promise(async (resolve, reject) => {
      this.startTimer(label)
      try {
        const result = await operation()
        this.endTimer(label)
        resolve(result)
      } catch (error) {
        this.endTimer(label)
        reject(error)
      }
    })
  }
}

// Cache LRU simple
export class LRUCache<T> {
  private cache = new Map<string, { value: T, timestamp: number }>()
  private maxSize: number
  private ttl: number
  
  constructor(maxSize = PERFORMANCE_CONFIG.MAX_CACHE_SIZE, ttl = PERFORMANCE_CONFIG.CACHE_DURATION) {
    this.maxSize = maxSize
    this.ttl = ttl
  }
  
  get(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null
    
    // Vérifier l'expiration
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }
    
    // Déplacer en fin (LRU)
    this.cache.delete(key)
    this.cache.set(key, item)
    
    return item.value
  }
  
  set(key: string, value: T): void {
    // Supprimer l'ancienne valeur si elle existe
    if (this.cache.has(key)) {
      this.cache.delete(key)
    }
    
    // Supprimer le plus ancien si la taille max est atteinte
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(key, { value, timestamp: Date.now() })
  }
  
  clear(): void {
    this.cache.clear()
  }
  
  size(): number {
    return this.cache.size
  }
}

// Debounce utility
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number = PERFORMANCE_CONFIG.DEBOUNCE_DELAY
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

// Throttle utility
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}
