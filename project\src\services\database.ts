import { supabase, DatabaseProject, DatabaseAudioAnalysis, DatabaseSeparationSettings, DatabaseStem, DatabaseProcessingResult } from '../lib/supabase'
import { AudioAnalysis, SeparationSettings, Stem, ProcessingResult } from '../types/audio'

export class DatabaseService {
  // Projets
  static async createProject(name: string, originalFilename: string, fileSize: number, duration: number): Promise<string> {
    const { data, error } = await supabase
      .from('projects')
      .insert({
        name,
        original_filename: originalFilename,
        file_size: fileSize,
        duration,
        status: 'uploaded'
      })
      .select('id')
      .single()

    if (error) {
      console.error('Erreur lors de la création du projet:', error)
      throw error
    }

    return data.id
  }

  static async getProject(id: string): Promise<DatabaseProject | null> {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Erreur lors de la récupération du projet:', error)
      return null
    }

    return data
  }

  static async getAllProjects(): Promise<DatabaseProject[]> {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Erreur lors de la récupération des projets:', error)
      return []
    }

    return data || []
  }

  static async updateProjectStatus(id: string, status: DatabaseProject['status']): Promise<void> {
    const { error } = await supabase
      .from('projects')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', id)

    if (error) {
      console.error('Erreur lors de la mise à jour du statut:', error)
      throw error
    }
  }

  // Analyses audio
  static async saveAudioAnalysis(projectId: string, analysis: AudioAnalysis): Promise<void> {
    const { error } = await supabase
      .from('audio_analyses')
      .insert({
        project_id: projectId,
        sample_rate: analysis.sampleRate,
        duration: analysis.duration,
        channels: analysis.channels,
        bit_rate: analysis.bitRate,
        format: analysis.format,
        loudness: analysis.loudness,
        dynamic_range: analysis.dynamicRange,
        spectral_centroid: analysis.spectralCentroid
      })

    if (error) {
      console.error('Erreur lors de la sauvegarde de l\'analyse:', error)
      throw error
    }
  }

  static async getAudioAnalysis(projectId: string): Promise<AudioAnalysis | null> {
    const { data, error } = await supabase
      .from('audio_analyses')
      .select('*')
      .eq('project_id', projectId)
      .single()

    if (error) {
      console.error('Erreur lors de la récupération de l\'analyse:', error)
      return null
    }

    return {
      sampleRate: data.sample_rate,
      duration: data.duration,
      channels: data.channels,
      bitRate: data.bit_rate,
      format: data.format,
      loudness: data.loudness,
      dynamicRange: data.dynamic_range,
      spectralCentroid: data.spectral_centroid
    }
  }

  // Paramètres de séparation
  static async saveSeparationSettings(projectId: string, settings: SeparationSettings): Promise<void> {
    const { error } = await supabase
      .from('separation_settings')
      .insert({
        project_id: projectId,
        model: settings.model,
        quality: settings.quality,
        stem_count: settings.stemCount,
        vocals_enabled: settings.vocals.enabled,
        vocals_denoising: settings.vocals.denoising,
        vocals_enhancement: settings.vocals.enhancement,
        vocals_pitch_correction: settings.vocals.pitch_correction,
        instruments_enabled: settings.instruments.enabled,
        instruments_separation_level: settings.instruments.separation_level,
        post_processing_normalize: settings.postProcessing.normalize,
        post_processing_fade_in: settings.postProcessing.fade_in,
        post_processing_fade_out: settings.postProcessing.fade_out,
        post_processing_noise_reduction: settings.postProcessing.noise_reduction
      })

    if (error) {
      console.error('Erreur lors de la sauvegarde des paramètres:', error)
      throw error
    }
  }

  static async getSeparationSettings(projectId: string): Promise<SeparationSettings | null> {
    const { data, error } = await supabase
      .from('separation_settings')
      .select('*')
      .eq('project_id', projectId)
      .single()

    if (error) {
      console.error('Erreur lors de la récupération des paramètres:', error)
      return null
    }

    return {
      model: data.model as SeparationSettings['model'],
      quality: data.quality as SeparationSettings['quality'],
      stemCount: data.stem_count as SeparationSettings['stemCount'],
      vocals: {
        enabled: data.vocals_enabled,
        denoising: data.vocals_denoising,
        enhancement: data.vocals_enhancement,
        pitch_correction: data.vocals_pitch_correction
      },
      instruments: {
        enabled: data.instruments_enabled,
        separation_level: data.instruments_separation_level as 'basic' | 'advanced'
      },
      postProcessing: {
        normalize: data.post_processing_normalize,
        fade_in: data.post_processing_fade_in,
        fade_out: data.post_processing_fade_out,
        noise_reduction: data.post_processing_noise_reduction
      }
    }
  }

  // Stems
  static async saveStems(projectId: string, stems: Stem[]): Promise<void> {
    const stemsData = stems.map(stem => ({
      project_id: projectId,
      name: stem.name,
      type: stem.type,
      color: stem.color,
      quality: stem.quality,
      confidence: stem.confidence,
      file_url: stem.audioUrl,
      peak_level: stem.metadata.peakLevel,
      rms_level: stem.metadata.rmsLevel,
      dynamic_range: stem.metadata.dynamicRange,
      spectral_centroid: stem.metadata.spectralCentroid
    }))

    const { error } = await supabase
      .from('stems')
      .insert(stemsData)

    if (error) {
      console.error('Erreur lors de la sauvegarde des stems:', error)
      throw error
    }
  }

  static async getStems(projectId: string): Promise<Stem[]> {
    const { data, error } = await supabase
      .from('stems')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('Erreur lors de la récupération des stems:', error)
      return []
    }

    return data.map(stem => ({
      id: stem.id,
      name: stem.name,
      type: stem.type as Stem['type'],
      audioUrl: stem.file_url,
      color: stem.color,
      quality: stem.quality,
      confidence: stem.confidence,
      metadata: {
        peakLevel: stem.peak_level,
        rmsLevel: stem.rms_level,
        dynamicRange: stem.dynamic_range,
        spectralCentroid: stem.spectral_centroid
      }
    }))
  }

  // Résultats de traitement
  static async createProcessingResult(projectId: string, model: string, quality: string): Promise<string> {
    const { data, error } = await supabase
      .from('processing_results')
      .insert({
        project_id: projectId,
        model,
        quality,
        status: 'pending',
        processing_time: 0
      })
      .select('id')
      .single()

    if (error) {
      console.error('Erreur lors de la création du résultat:', error)
      throw error
    }

    return data.id
  }

  static async updateProcessingResult(id: string, updates: Partial<DatabaseProcessingResult>): Promise<void> {
    const { error } = await supabase
      .from('processing_results')
      .update(updates)
      .eq('id', id)

    if (error) {
      console.error('Erreur lors de la mise à jour du résultat:', error)
      throw error
    }
  }

  static async getProcessingResults(projectId: string): Promise<DatabaseProcessingResult[]> {
    const { data, error } = await supabase
      .from('processing_results')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Erreur lors de la récupération des résultats:', error)
      return []
    }

    return data || []
  }
}
