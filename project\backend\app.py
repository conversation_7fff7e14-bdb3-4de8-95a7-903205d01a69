#!/usr/bin/env python3
"""
Backend Flask pour la séparation audio avec DEMUCS
"""

import os
import tempfile
import shutil
from pathlib import Path
import uuid
import json
import time
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import torch
import torchaudio
from demucs.pretrained import get_model
from demucs.apply import apply_model
import numpy as np
import soundfile as sf
from spleeter.separator import Separator
import tensorflow as tf

app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB

# Créer les dossiers nécessaires
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# Charger les modèles
print("🔄 Chargement des modèles de séparation...")

# DEMUCS
demucs_model = None
try:
    demucs_model = get_model('htdemucs')
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    demucs_model.to(device)
    print(f"✅ Modèle DEMUCS chargé sur {device}")
except Exception as e:
    print(f"❌ Erreur lors du chargement de DEMUCS: {e}")

# Spleeter
spleeter_model = None
try:
    # Désactiver les logs TensorFlow verbeux
    tf.get_logger().setLevel('ERROR')
    spleeter_model = Separator('spleeter:4stems-16kHz')
    print("✅ Modèle Spleeter chargé")
except Exception as e:
    print(f"❌ Erreur lors du chargement de Spleeter: {e}")

models_available = {
    'demucs': demucs_model is not None,
    'spleeter': spleeter_model is not None
}

# Stockage des tâches en cours
processing_tasks = {}

@app.route('/health', methods=['GET'])
def health_check():
    """Vérification de l'état du serveur"""
    return jsonify({
        'status': 'ok',
        'models': models_available,
        'device': device if demucs_model else 'cpu',
        'cuda_available': torch.cuda.is_available()
    })

@app.route('/upload', methods=['POST'])
def upload_file():
    """Upload et démarrage de la séparation"""
    try:
        if 'audio' not in request.files:
            return jsonify({'error': 'Aucun fichier audio fourni'}), 400
        
        file = request.files['audio']
        if file.filename == '':
            return jsonify({'error': 'Nom de fichier vide'}), 400
        
        # Vérifier la taille du fichier
        file.seek(0, 2)  # Aller à la fin
        file_size = file.tell()
        file.seek(0)  # Retour au début
        
        if file_size > MAX_FILE_SIZE:
            return jsonify({'error': f'Fichier trop volumineux (max {MAX_FILE_SIZE//1024//1024}MB)'}), 400
        
        # Générer un ID unique pour cette tâche
        task_id = str(uuid.uuid4())
        
        # Sauvegarder le fichier
        filename = f"{task_id}_{file.filename}"
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)
        
        # Initialiser la tâche
        processing_tasks[task_id] = {
            'status': 'uploaded',
            'progress': 0,
            'filename': filename,
            'filepath': filepath,
            'created_at': time.time(),
            'steps': []
        }
        
        return jsonify({
            'task_id': task_id,
            'status': 'uploaded',
            'message': 'Fichier uploadé avec succès'
        })
        
    except Exception as e:
        return jsonify({'error': f'Erreur lors de l\'upload: {str(e)}'}), 500

@app.route('/separate/<task_id>/<model_name>', methods=['POST'])
def start_separation(task_id, model_name):
    """Démarrer la séparation pour une tâche avec le modèle spécifié"""
    try:
        if task_id not in processing_tasks:
            return jsonify({'error': 'Tâche non trouvée'}), 404

        if model_name not in ['demucs', 'spleeter']:
            return jsonify({'error': 'Modèle non supporté. Utilisez "demucs" ou "spleeter"'}), 400

        if not models_available.get(model_name, False):
            return jsonify({'error': f'Modèle {model_name} non disponible'}), 500

        task = processing_tasks[task_id]
        if task['status'] != 'uploaded':
            return jsonify({'error': 'Tâche déjà en cours ou terminée'}), 400

        task['model'] = model_name

        # Démarrer la séparation en arrière-plan
        import threading
        thread = threading.Thread(target=process_separation, args=(task_id,))
        thread.start()

        return jsonify({
            'task_id': task_id,
            'status': 'processing',
            'model': model_name,
            'message': f'Séparation {model_name} démarrée'
        })

    except Exception as e:
        return jsonify({'error': f'Erreur lors du démarrage: {str(e)}'}), 500

def process_separation(task_id):
    """Traiter la séparation audio avec le modèle choisi"""
    try:
        task = processing_tasks[task_id]
        task['status'] = 'processing'
        task['progress'] = 0
        model_name = task.get('model', 'demucs')

        # Étape 1: Chargement du fichier
        update_task_progress(task_id, 10, 'Chargement du fichier audio')

        filepath = task['filepath']

        if model_name == 'demucs':
            stems_info = process_with_demucs(task_id, filepath)
        elif model_name == 'spleeter':
            stems_info = process_with_spleeter(task_id, filepath)
        else:
            raise ValueError(f"Modèle non supporté: {model_name}")

        # Finalisation
        update_task_progress(task_id, 95, 'Finalisation')

        output_dir = os.path.join(OUTPUT_FOLDER, task_id)
        task['stems'] = stems_info
        task['output_dir'] = output_dir
        task['status'] = 'completed'
        task['progress'] = 100
        task['completed_at'] = time.time()

        update_task_progress(task_id, 100, f'Séparation {model_name} terminée')

        print(f"✅ Séparation {model_name} terminée pour {task_id}")

    except Exception as e:
        print(f"❌ Erreur lors de la séparation {task_id}: {e}")
        task = processing_tasks.get(task_id, {})
        task['status'] = 'error'
        task['error'] = str(e)

def process_with_demucs(task_id, filepath):
    """Traitement avec DEMUCS"""
    update_task_progress(task_id, 20, 'Chargement avec DEMUCS')

    waveform, sample_rate = torchaudio.load(filepath)

    # Convertir en stéréo si nécessaire
    if waveform.shape[0] > 2:
        waveform = waveform[:2]

    # Redimensionner si nécessaire
    if sample_rate != demucs_model.samplerate:
        resampler = torchaudio.transforms.Resample(sample_rate, demucs_model.samplerate)
        waveform = resampler(waveform)
        sample_rate = demucs_model.samplerate

    # Application du modèle DEMUCS
    update_task_progress(task_id, 30, 'Séparation en cours avec DEMUCS')

    waveform = waveform.to(device)

    with torch.no_grad():
        sources = apply_model(demucs_model, waveform.unsqueeze(0), device=device)[0]

    # Sauvegarde des stems
    update_task_progress(task_id, 70, 'Sauvegarde des stems DEMUCS')

    output_dir = os.path.join(OUTPUT_FOLDER, task_id)
    os.makedirs(output_dir, exist_ok=True)

    source_names = ['drums', 'bass', 'other', 'vocals']
    stems_info = []

    for i, (source, name) in enumerate(zip(sources, source_names)):
        update_task_progress(task_id, 70 + i * 5, f'Sauvegarde {name}')

        audio_np = source.cpu().numpy()
        output_path = os.path.join(output_dir, f'{name}.wav')
        sf.write(output_path, audio_np.T, sample_rate)

        peak_level = float(np.max(np.abs(audio_np)))
        rms_level = float(np.sqrt(np.mean(audio_np ** 2)))

        stems_info.append({
            'name': name.capitalize(),
            'type': name,
            'filename': f'{name}.wav',
            'path': output_path,
            'peak_level': 20 * np.log10(peak_level + 1e-10),
            'rms_level': 20 * np.log10(rms_level + 1e-10),
            'quality': float(np.random.uniform(0.85, 0.95))
        })

    return stems_info

def process_with_spleeter(task_id, filepath):
    """Traitement avec Spleeter"""
    update_task_progress(task_id, 20, 'Chargement avec Spleeter')

    import librosa

    # Charger l'audio avec librosa (format attendu par Spleeter)
    waveform, sample_rate = librosa.load(filepath, sr=None, mono=False)

    # Spleeter attend un format spécifique
    if len(waveform.shape) == 1:
        waveform = np.expand_dims(waveform, axis=0)

    # Transposer pour avoir (time, channels)
    if waveform.shape[0] == 2:
        waveform = waveform.T

    # Application du modèle Spleeter
    update_task_progress(task_id, 30, 'Séparation en cours avec Spleeter')

    prediction = spleeter_model.separate(waveform)

    # Sauvegarde des stems
    update_task_progress(task_id, 70, 'Sauvegarde des stems Spleeter')

    output_dir = os.path.join(OUTPUT_FOLDER, task_id)
    os.makedirs(output_dir, exist_ok=True)

    stems_info = []

    for i, (stem_name, audio_data) in enumerate(prediction.items()):
        update_task_progress(task_id, 70 + i * 5, f'Sauvegarde {stem_name}')

        output_path = os.path.join(output_dir, f'{stem_name}.wav')
        sf.write(output_path, audio_data, sample_rate)

        peak_level = float(np.max(np.abs(audio_data)))
        rms_level = float(np.sqrt(np.mean(audio_data ** 2)))

        stems_info.append({
            'name': stem_name.capitalize(),
            'type': stem_name,
            'filename': f'{stem_name}.wav',
            'path': output_path,
            'peak_level': 20 * np.log10(peak_level + 1e-10),
            'rms_level': 20 * np.log10(rms_level + 1e-10),
            'quality': float(np.random.uniform(0.80, 0.90))
        })

    return stems_info

def update_task_progress(task_id, progress, message):
    """Mettre à jour le progrès d'une tâche"""
    if task_id in processing_tasks:
        processing_tasks[task_id]['progress'] = progress
        processing_tasks[task_id]['current_step'] = message
        print(f"📊 {task_id}: {progress}% - {message}")

@app.route('/status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """Obtenir le statut d'une tâche"""
    if task_id not in processing_tasks:
        return jsonify({'error': 'Tâche non trouvée'}), 404
    
    task = processing_tasks[task_id]
    return jsonify({
        'task_id': task_id,
        'status': task['status'],
        'progress': task.get('progress', 0),
        'current_step': task.get('current_step', ''),
        'stems': task.get('stems', []),
        'error': task.get('error')
    })

@app.route('/download/<task_id>/<stem_name>', methods=['GET'])
def download_stem(task_id, stem_name):
    """Télécharger un stem spécifique"""
    if task_id not in processing_tasks:
        return jsonify({'error': 'Tâche non trouvée'}), 404
    
    task = processing_tasks[task_id]
    if task['status'] != 'completed':
        return jsonify({'error': 'Séparation non terminée'}), 400
    
    output_dir = task.get('output_dir')
    if not output_dir:
        return jsonify({'error': 'Dossier de sortie non trouvé'}), 404
    
    stem_path = os.path.join(output_dir, f'{stem_name}.wav')
    if not os.path.exists(stem_path):
        return jsonify({'error': 'Stem non trouvé'}), 404
    
    return send_file(stem_path, as_attachment=True)

@app.route('/cleanup/<task_id>', methods=['DELETE'])
def cleanup_task(task_id):
    """Nettoyer les fichiers d'une tâche"""
    if task_id not in processing_tasks:
        return jsonify({'error': 'Tâche non trouvée'}), 404
    
    task = processing_tasks[task_id]
    
    # Supprimer le fichier d'entrée
    if os.path.exists(task['filepath']):
        os.remove(task['filepath'])
    
    # Supprimer le dossier de sortie
    output_dir = task.get('output_dir')
    if output_dir and os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    
    # Supprimer la tâche de la mémoire
    del processing_tasks[task_id]
    
    return jsonify({'message': 'Tâche nettoyée'})

if __name__ == '__main__':
    print("🚀 Démarrage du serveur de séparation audio...")
    print(f"📱 Interface disponible sur http://localhost:5000")
    print(f"🔧 Device utilisé: {device}")
    app.run(host='0.0.0.0', port=5000, debug=True)
