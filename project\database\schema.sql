-- Création des tables pour l'application de séparation audio

-- Table des projets
CREATE TABLE IF NOT EXISTS projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    original_filename VA<PERSON>HAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    duration FLOAT NOT NULL,
    status VARCHAR(20) DEFAULT 'uploaded' CHECK (status IN ('uploaded', 'processing', 'completed', 'error')),
    user_id UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des analyses audio
CREATE TABLE IF NOT EXISTS audio_analyses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    sample_rate INTEGER NOT NULL,
    duration FLOAT NOT NULL,
    channels INTEGER NOT NULL,
    bit_rate INTEGER NOT NULL,
    format VARCHAR(10) NOT NULL,
    loudness FLOAT NOT NULL,
    dynamic_range FLOAT NOT NULL,
    spectral_centroid FLOAT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des paramètres de séparation
CREATE TABLE IF NOT EXISTS separation_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    model VARCHAR(50) NOT NULL,
    quality VARCHAR(20) NOT NULL,
    stem_count INTEGER NOT NULL,
    vocals_enabled BOOLEAN DEFAULT true,
    vocals_denoising BOOLEAN DEFAULT true,
    vocals_enhancement BOOLEAN DEFAULT true,
    vocals_pitch_correction BOOLEAN DEFAULT false,
    instruments_enabled BOOLEAN DEFAULT true,
    instruments_separation_level VARCHAR(20) DEFAULT 'advanced',
    post_processing_normalize BOOLEAN DEFAULT true,
    post_processing_fade_in FLOAT DEFAULT 0,
    post_processing_fade_out FLOAT DEFAULT 0,
    post_processing_noise_reduction INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des stems (pistes séparées)
CREATE TABLE IF NOT EXISTS stems (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL,
    color VARCHAR(7) NOT NULL,
    quality FLOAT NOT NULL,
    confidence FLOAT NOT NULL,
    file_url TEXT,
    peak_level FLOAT NOT NULL,
    rms_level FLOAT NOT NULL,
    dynamic_range FLOAT NOT NULL,
    spectral_centroid FLOAT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des résultats de traitement
CREATE TABLE IF NOT EXISTS processing_results (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    processing_time INTEGER DEFAULT 0,
    model VARCHAR(50) NOT NULL,
    quality VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'error')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);
CREATE INDEX IF NOT EXISTS idx_audio_analyses_project_id ON audio_analyses(project_id);
CREATE INDEX IF NOT EXISTS idx_separation_settings_project_id ON separation_settings(project_id);
CREATE INDEX IF NOT EXISTS idx_stems_project_id ON stems(project_id);
CREATE INDEX IF NOT EXISTS idx_processing_results_project_id ON processing_results(project_id);
CREATE INDEX IF NOT EXISTS idx_processing_results_status ON processing_results(status);

-- Politiques de sécurité RLS (Row Level Security)
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE separation_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE stems ENABLE ROW LEVEL SECURITY;
ALTER TABLE processing_results ENABLE ROW LEVEL SECURITY;

-- Politiques pour les projets
CREATE POLICY "Users can view their own projects" ON projects
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert their own projects" ON projects
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can update their own projects" ON projects
    FOR UPDATE USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can delete their own projects" ON projects
    FOR DELETE USING (auth.uid() = user_id OR user_id IS NULL);

-- Politiques pour les analyses audio
CREATE POLICY "Users can view analyses of their projects" ON audio_analyses
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = audio_analyses.project_id 
            AND (projects.user_id = auth.uid() OR projects.user_id IS NULL)
        )
    );

CREATE POLICY "Users can insert analyses for their projects" ON audio_analyses
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = audio_analyses.project_id 
            AND (projects.user_id = auth.uid() OR projects.user_id IS NULL)
        )
    );

-- Politiques similaires pour les autres tables
CREATE POLICY "Users can view settings of their projects" ON separation_settings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = separation_settings.project_id 
            AND (projects.user_id = auth.uid() OR projects.user_id IS NULL)
        )
    );

CREATE POLICY "Users can insert settings for their projects" ON separation_settings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = separation_settings.project_id 
            AND (projects.user_id = auth.uid() OR projects.user_id IS NULL)
        )
    );

CREATE POLICY "Users can view stems of their projects" ON stems
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = stems.project_id 
            AND (projects.user_id = auth.uid() OR projects.user_id IS NULL)
        )
    );

CREATE POLICY "Users can insert stems for their projects" ON stems
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = stems.project_id 
            AND (projects.user_id = auth.uid() OR projects.user_id IS NULL)
        )
    );

CREATE POLICY "Users can view results of their projects" ON processing_results
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = processing_results.project_id 
            AND (projects.user_id = auth.uid() OR projects.user_id IS NULL)
        )
    );

CREATE POLICY "Users can insert results for their projects" ON processing_results
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = processing_results.project_id 
            AND (projects.user_id = auth.uid() OR projects.user_id IS NULL)
        )
    );

CREATE POLICY "Users can update results of their projects" ON processing_results
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = processing_results.project_id 
            AND (projects.user_id = auth.uid() OR projects.user_id IS NULL)
        )
    );

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger pour mettre à jour updated_at sur les projets
CREATE TRIGGER update_projects_updated_at 
    BEFORE UPDATE ON projects 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
